<?xml version="1.0"?>
<ruleset name="Acme">
	<file>./app/PostType</file>
	<file>./app/Model/Product/Score</file>
	<file>./app/Model/Currency</file>
	<file>./app/Console</file>
	<file>./app/Model/Erp</file>
	<file>./app/Model/Sitemap</file>
	<file>./app/Model/Orm/Order</file>
	<file>./app/Model/Orm/PaymentMethod</file>
	<file>./app/Model/Orm/IpAddress</file>
	<file>./app/Model/Orm/DeliveryMethod</file>
	<file>./app/Model/Orm/CardPayment</file>
	<file>./app/Model/Orm/Watchdog</file>
	<file>./app/Model/Orm/ClassEvent</file>
	<file>./app/Model/Orm/ClassSection</file>
	<file>./app/Model/Orm/ClassEventSectionMetadata</file>
	<file>./app/Model/Price</file>
	<file>./app/FrontModule/Components/ProductList</file>
	<file>./app/AdminModule/Presenters/PaymentMethod</file>
	<file>./app/AdminModule/Presenters/Parameter</file>
	<file>./app/AdminModule/Presenters/DeliveryMethod</file>
	<file>./app/FrontModule/Components/OrderCreateUser</file>
	<file>./app/FrontModule/Components/SiteHeader</file>
	<file>./app/FrontModule/Components/FacebookLogin</file>
	<file>./app/FrontModule/Components/MyLibrary</file>
	<file>./app/FrontModule/Components/AddToMyLibrary</file>
	<file>./app/FrontModule/Components/BoughtProducts</file>
	<file>./app/FrontModule/Components/SeznamLogin</file>
	<file>./app/FrontModule/Presenters/Order</file>
	<file>./app/FrontModule/Components/CartGift</file>
	<file>./app/FrontModule/Components/CartDelivery</file>
	<file>./app/FrontModule/Components/Suggest</file>
	<file>./app/FrontModule/Components/ProductBox</file>
	<file>./app/FrontModule/Components/CatalogProducts</file>
	<file>./app/AdminModule/Presenters/Mutation/Components/SynonymsDataGrid</file>
	<file>./app/AdminModule/Presenters/Voucher</file>
	<file>./app/AdminModule/Presenters/Class</file>
	<file>./app/AdminModule/Presenters/ClassSection</file>
	<file>./app/AdminModule/Presenters/ClassEvent</file>
	<file>./app/Model/FeedGenerator</file>
	<file>./app/Model/DTO</file>
	<file>./app/Model/Time</file>
	<file>./app/Model/Messenger</file>
	<file>./app/Model/TagManager</file>
	<file>./app/Model/Cache</file>
	<file>./app/Model/FulltextSearch</file>
	<file>./app/Model/Router</file>
	<file>./app/Event</file>
	<file>./app/Model/Promotion</file>
	<file>./app/Model/Link</file>
	<file>./app/Scheduler</file>
	<file>./app/FrontModule/Components/MyLibrary</file>
	<file>./app/FrontModule/Components/OpenGraph</file>
	<file>./app/Exponea</file>
	<file>./app/FrontModule/Components/Watchdog</file>
	<file>./app/FrontModule/Components/MyWatchdog</file>
	<file>./app/FrontModule/Components/VariantPicker</file>
	<file>./app/Model/Odoo</file>
	<file>./app/FrontModule/Components/SavedForLater</file>
	<file>./app/FrontModule/Components/PaidByLo</file>
	<file>./app/FrontModule/Components/PaidByLoQuestionare</file>
	<file>./app/FrontModule/Presenters/PaidByLo</file>
	<file>./app/Model/Orm/Lead</file>
	<file>./app/Model/Orm/Usp</file>

	<exclude-pattern>./app/config</exclude-pattern>
	<exclude-pattern>./app/PostType/Page/AdminModule</exclude-pattern>
	<exclude-pattern>./app/PostType/Core</exclude-pattern>
	<exclude-pattern>./app/PostType/SeoLink</exclude-pattern>
	<exclude-pattern>./app/PostType/BlogTag</exclude-pattern>
	<exclude-pattern>./app/PostType/Blog</exclude-pattern>
	<exclude-pattern>./app/PostType/Author</exclude-pattern>
	<exclude-pattern>./app/PostType/Brand</exclude-pattern>
	<exclude-pattern>./app/Scheduler/Example</exclude-pattern>

	<file>./app/PostType/Page/Model/Orm/Event</file>

	<arg name="extensions" value="php,phpt"/>
	<arg name="colors"/>
	<arg value="nsp"/>

	<!-- Contributte Coding Standard -->
	<rule ref="./vendor/ninjify/coding-standard/contributte.xml">
		<exclude name="SlevomatCodingStandard.ControlStructures.EarlyExit.EarlyExitNotUsed"/>
		<exclude name="SlevomatCodingStandard.Classes.SuperfluousAbstractClassNaming.SuperfluousSuffix" />
		<exclude name="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming.SuperfluousSuffix" />
		<exclude name="SlevomatCodingStandard.Classes.RequireMultiLineMethodSignature.RequiredMultiLineSignature" />
		<exclude name="Generic.WhiteSpace.ScopeIndent" /><!-- error: Property "spaceIndent" does not exist on sniff Generic.WhiteSpace.ScopeIndent -->
		<exclude name="SlevomatCodingStandard.TypeHints.DeclareStrictTypes" /><!-- error: Property "newlinesCountBetweenOpenTagAndDeclare" does not exist on sniff SlevomatCodingStandard.TypeHints.DeclareStrictTypes -->
		<exclude name="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing" /><!-- error: Property "tokensToCheck" does not exist on sniff SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing -->
		<exclude name="SlevomatCodingStandard.Classes.TraitUseDeclaration.MultipleTraitsPerDeclaration"/>
	</rule>

	<rule ref="SlevomatCodingStandard.Namespaces.FullyQualifiedClassNameInAnnotation">
		<exclude name="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly.ReferenceViaFullyQualifiedName" />
	</rule>

	<rule ref="Generic.NamingConventions.UpperCaseConstantName">
		<exclude name="Generic.NamingConventions.UpperCaseConstantName.ClassConstantNotUpperCase"/>
	</rule>

	<!-- Slevomat Conding Standart -->
	<rule ref="SlevomatCodingStandard.Files.TypeNameMatchesFileName">
		<severity>0</severity>
	</rule>
	<!-- PSR naming conventions -> https://www.php-fig.org/bylaws/psr-naming-conventions/ -->
	<rule ref="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming.SuperfluousSuffix">
		<severity>0</severity>
	</rule>
	<rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses.IncorrectlyOrderedUses">
		<severity>0</severity>
	</rule>
	<rule ref="Generic.Files.LineEndings.InvalidEOLChar">
		<severity>0</severity>
	</rule>
</ruleset>
