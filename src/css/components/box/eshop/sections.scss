@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-sections {
	margin: 0 0 3.2rem;
	padding-bottom: 3.2rem;
	border-bottom: 0.1rem solid variables.$color-tile-light;
	*:target {
		scroll-margin-top: variables.$row-main-gutter;
	}
	&__title-wrap {
		display: flex;
		gap: 1rem;
		align-items: center;
	}
	&__content h3 {
		--font-size-mobile: 1.6rem;
		--font-size-desktop: 1.8rem;
	}
	&__flag {
		.icon-svg--star {
			top: -0.05em;
			color: variables.$color-alert;
		}
		&.flag--sm {
			--flag-fs: 1.1rem;
			--flag-h: 2.2rem;
		}
	}
	&__carousel {
		overflow: visible;
		.grid {
			--grid-x-spacing: 0.8rem;
		}
		.grid__cell {
			width: calc(29rem + var(--grid-x-spacing));
		}
	}

	// MQ
	@media (config.$lg-down) {
		&__menu,
		&__name {
			display: none;
		}
		&__title-wrap {
			min-height: 5.9rem;
			margin: 0 0 0.4rem;
			padding: 1rem 2rem 1rem 1.6rem;
			border-radius: variables.$border-radius-md;
			background: variables.$color-bg;
		}
		&__title {
			color: variables.$color-text;
			font-family: variables.$font-primary;
			font-size: 1.5rem;
		}
		&__arrow {
			margin-left: auto;
			transition: transform variables.$t;
		}
		&__box {
			padding: 1.2rem 0 2.4rem;
		}

		// STATES
		&__section:not(.is-open) &__box {
			display: none;
		}
		&__section.is-open &__arrow {
			transform: scale(-1);
		}
		&__box:has(.b-content) {
			padding: 1.2rem 0.8rem 2.4rem;
		}
	}
	@media (config.$md-down) {
		&__carousel {
			--arrow-position: -0.7rem;
			&.c-articles-carousel {
				width: calc(30.6rem + var(--grid-x-spacing));
			}
		}
	}
	@media (config.$md-up) {
		&__carousel {
			.grid {
				--grid-x-spacing: 2rem;
			}
		}
	}
	@media (config.$lg-up) {
		--menu-width: 28rem;
		--gap: 4rem;
		display: flex;
		gap: var(--gap);
		align-items: flex-start;
		margin-bottom: 7.2rem;
		padding-bottom: 10rem;
		&__menu {
			position: sticky;
			top: variables.$row-main-gutter;
			flex: 0 0 auto;
			width: var(--menu-width);
			transition: top variables.$t;
		}
		&__main {
			flex: 0 0 auto;
			width: calc(100% - var(--menu-width) - var(--gap));
		}
		&__section {
			margin-bottom: 8rem;
			padding-bottom: 8rem;
			border-bottom: 0.1rem solid variables.$color-tile-light;
			&:last-child {
				margin: 0;
				padding: 0;
				border: none;
			}
		}
		&__title-wrap {
			gap: 1.2rem;
			margin: 0 0 2.4rem;
		}
		&__name {
			color: variables.$color-placeholder;
		}
		&__arrow {
			display: none;
		}
		&__carousel {
			--arrow-position: -2.6rem;
			.embla__viewport {
				margin-right: min(
					calc((var(--vw) - variables.$row-main-width) / -2 - variables.$row-main-gutter),
					calc(var(--row-main-gutter) * -1)
				);
				overflow: hidden;
			}
			.embla__progress {
				max-width: none;
				margin-top: 2.4rem;
			}
			.embla__btn--next {
				right: min(
					calc((var(--vw) - variables.$row-main-width) / -2 - variables.$row-main-gutter + 2rem),
					calc(var(--row-main-gutter) * -1 + 2rem)
				);
			}
		}

		// MODIF
		.b-product-detail + & {
			padding-top: 10rem;
			border-top: 0.1rem solid variables.$color-tile-light;
		}
	}
	@media (config.$xxxl-up) {
		--gap: 12rem;
		--menu-width: 36.8rem;
	}
}
