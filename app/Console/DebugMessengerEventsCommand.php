<?php

namespace App\Console;

// src/Command/DebugMessengerEventsCommand.php
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

#[AsCommand(
	name:'debug:messenger-events',
	description: 'Debug messenger events'
)]
final class DebugMessengerEventsCommand extends Command
{

	public function __construct(
		private readonly EventDispatcherInterface $eventDispatcher
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$messengerEvents = [
			'Symfony\Component\Messenger\Event\WorkerMessageReceivedEvent',
			'Symfony\Component\Messenger\Event\WorkerMessageHandledEvent',
			'Symfony\Component\Messenger\Event\WorkerMessageFailedEvent',
			'Symfony\Component\Messenger\Event\WorkerRunningEvent',
			'Symfony\Component\Messenger\Event\WorkerStartedEvent',
			'Symfony\Component\Messenger\Event\WorkerStoppedEvent',
			'Symfony\Component\Messenger\Event\SendMessageToTransportsEvent',
		];

		foreach ($messengerEvents as $eventClass) {
			$output->writeln('<info>' . $eventClass . '</info>');

			if ($this->eventDispatcher->hasListeners($eventClass)) {
				$listeners = $this->eventDispatcher->getListeners($eventClass);
				foreach ($listeners as $listener) {
					$listenerName = $this->getListenerName($listener);
					$output->writeln('  - ' . $listenerName);
				}
			} else {
				$output->writeln('  <comment>No listeners</comment>');
			}
			$output->writeln('');
		}

		return Command::SUCCESS;
	}

	private function getListenerName(mixed $listener): string
	{
		if (is_array($listener) && is_object($listener[0])) {
			return get_class($listener[0]) . '::' . $listener[1];
		}

		if (is_object($listener)) {
			return get_class($listener);
		}

		return (string) $listener;
	}

}
