<?php

declare(strict_types = 1);

namespace App\Console\Feeds;

use App\Console\BaseCommand;
use App\Model\ConfigService;
use App\Model\FeedGenerator\Individual\Factories\IndividualFactorySetup;
use App\Model\FeedGenerator\Individual\IndividualFeedGenerator;
use App\Model\Orm\Orm;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'feeds:generate:customFeed',
	description: 'Generate custom feed',
)]
#[AsCronTask('0 */3 * * *')]
class GenerateCustomFeedCommand extends BaseCommand
{

	public function __construct(
		private readonly IndividualFactorySetup $individualFactorySetup,
		protected readonly ConfigService $configService,
		protected readonly Orm $orm,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->setIsLockable(true, $this->configService->get('env'));
		$this->start($input);
		$output->writeLn('START');

		$mutations = $this->orm->mutation->findAll()->fetchPairs(null, 'langCode');

		foreach ($mutations as $mutation) {
			$individualFeedGenerator = new IndividualFeedGenerator($this->individualFactorySetup->create(), $mutation);
			$individualFeedGenerator->generateCustomFeed(WWW_DIR . '/exports/mergado_feed_' . $mutation . '.xml');
		}

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
