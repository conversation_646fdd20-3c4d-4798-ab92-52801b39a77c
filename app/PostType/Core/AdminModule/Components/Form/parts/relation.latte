{varType App\Model\Translator $translator}

{php assert(isset($relationInfo) && $relationInfo instanceof App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo)}
{varType App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo $relationInfo}
{var $props = [
	title: $translator->translate($relationInfo->toggleName),
	id: $relationInfo->propertyName,
	icon: $templates . '/part/icons/' . $relationInfo->iconFilename,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
	listFormCointainer: $form[$relationInfo->propertyName],
	listPlaceholder: $relationInfo->inputPlaceHolder,
	listSearchUrl: $relationInfo->suggestUrl,
	listName: $relationInfo->propertyName . 'List',
	dragdrop: $relationInfo->dragAndDrop,
	showAdd:  ( ! $relationInfo->singleValue),
	singleValue:  $relationInfo->singleValue,
	disabled: $disabled ?? false,
]}

{include $templates.'/part/core/formList.latte', props: $props}

