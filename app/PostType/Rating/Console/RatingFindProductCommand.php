<?php

declare(strict_types = 1);

namespace App\PostType\Rating\Console;

use App\Console\BaseCommand;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\PostType\Rating\Model\Messanger\RatingFindProductMessage;
use App\PostType\Rating\Model\Orm\RatingLocalizationRepository;
use App\Scheduler\Attribute\AsScheduleEnvironment;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'rating:find-product',
	description: 'Find product for rating',
)]
#[AsCronTask(expression: '0 23 * * *', transports: 'cronCommands')]
#[AsScheduleEnvironment(environment: ['prod', 'stage'])]
class RatingFindProductCommand extends BaseCommand
{

	public function __construct(
		private readonly ElasticBusWrapper $elasticBusWrapper,
		private readonly RatingLocalizationRepository $ratingLocalizationRepository,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$ratingLocalizations = $this->ratingLocalizationRepository->findAll();

		foreach ($ratingLocalizations as $ratingLocalization) {
			$this->elasticBusWrapper->send(
				new RatingFindProductMessage(
					$ratingLocalization,
				)
			);
		}

		return $this->end(self::SUCCESS);
	}

}
