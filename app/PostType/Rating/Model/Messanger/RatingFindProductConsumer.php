<?php

declare(strict_types = 1);

namespace App\PostType\Rating\Model\Messanger;

use App\Model\Orm\Orm;
use App\PostType\Rating\Actions\AttachProductsWithRatingLocalization;
use App\PostType\Rating\Model\Orm\RatingLocalizationRepository;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class RatingFindProductConsumer
{

	public function __construct(
		private readonly Orm $orm,
		private readonly RatingLocalizationRepository $ratingLocalizationRepository,
		private readonly AttachProductsWithRatingLocalization $attachProductsWithRatingLocalization,
	)
	{
	}

	public function __invoke(RatingFindProductMessage $message): void
	{
		$ratingLocalizationEntity = $this->ratingLocalizationRepository->getById($message->ratingLocalizationId);

		if ($ratingLocalizationEntity === null) {
			return;
		}

		$this->orm->setMutation($ratingLocalizationEntity->mutation);

		$ratingLocalizationEntity->treeProductIds = $this->attachProductsWithRatingLocalization->handle($ratingLocalizationEntity);

		$this->ratingLocalizationRepository->persistAndFlush($ratingLocalizationEntity);
	}

}
