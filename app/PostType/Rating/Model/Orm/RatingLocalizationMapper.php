<?php declare(strict_types = 1);

namespace App\PostType\Rating\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<RatingLocalization>
 */
class RatingLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'rating_localization';
	}

	/**
	 * @return ICollection<RatingLocalization>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if (count($excluded) > 0) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<RatingLocalization>
	 */
	public function findRandom(): ICollection
	{
		$builder = $this->builder()
			->orderBy('RAND()');

		return $this->toCollection($builder);
	}

	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('a.id')
			->from($this->getTableName(), 'a')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('al.id')
			->from($this->getTableName(), 'al')
			->andWhere('al.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

	public function initSort(bool $forceInit = false): void
	{
		$builder = $this->builder();
		$builder->where('sort = %i', 0)->limitBy(1);

		$row = $this->connection->queryByQueryBuilder($builder)->fetch();

		if ($row !== null || $forceInit) {
			$b = $this->builder()->select('id')->orderBy('sort ASC');
			$i = 1;
			foreach ($this->connection->queryByQueryBuilder($b) as $parameter) {
				$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), $i, $parameter->id);
				$i++;
			}
		}
	}

	public function moveUp(int $siblingSort, int $sort): void
	{
		$b = $this->builder()->select('id, sort')->where('sort <= %i AND sort > %i', $siblingSort, $sort);
		foreach ($this->connection->queryByQueryBuilder($b) as $row) {
			$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), ($row->sort - 1), $row->id);
		}
	}

	public function moveDown(int $siblingSort, int $sort): void
	{
		$b = $this->builder()->select('id, sort')->where('sort >= %i AND sort < %i', $siblingSort, $sort);
		foreach ($this->connection->queryByQueryBuilder($b) as $row) {
			$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), ($row->sort + 1), $row->id);
		}
	}

}
