<?php declare(strict_types = 1);

namespace App\PostType\Rating\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method RatingLocalization|null getById($id)
 * @method ICollection<RatingLocalization> searchByName(string $q, array $excluded)
 * @method ICollection<RatingLocalization> findRandom()
 * @method array findAllIds(?int $limit)
 * @method void initSort(bool $forceInit = false)
 * @method void moveUp(int $siblingSort, int $sort)
 * @method void moveDown(int $siblingSort, int $sort)
 * @extends Repository<RatingLocalization>
 */
final class RatingLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [RatingLocalization::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof RatingLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
