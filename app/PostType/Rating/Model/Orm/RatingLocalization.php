<?php declare(strict_types = 1);

namespace App\PostType\Rating\Model\Orm;

use App\Model\CustomField\LazyValue;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use App\PostType\Page\Model\Orm\CatalogTree;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property int|null $sort {default 0}
 * @property array $treeProductIds {container JsonContainer}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property Rating $rating {M:1 Rating::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property CatalogTree|null $tree {m:1 CatalogTree::$ratingLocalizations}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 */
class RatingLocalization extends BaseEntity implements LocalizationEntity, Publishable, HasImages, Editable
{

	use HasCustomFields;
	use HasCustomContent;

	protected Orm $orm;

	protected Repository $repository;

	public function injectOrm(Orm $orm): void
	{
		$this->orm = $orm;
	}

	public function injectRepository(Repository $repository): void
	{
		$this->repository = $repository;
	}

	protected function getterTemplate(): string
	{
		return ':Rating:Admin:Rating:detail';
	}

	protected function getterPath(): array
	{
		return [];
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getParent(): Rating
	{
		return $this->rating;
	}

	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Rating);
		$this->rating = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->getParent()->cf->base->mainImage) ? $this->getParent()->cf->base->mainImage->getEntity() : null;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getProductIdByTree(CatalogTree $tree): int|null
	{
		$productIds = (array) $this->treeProductIds;
		return $this->getManualProduct()?->id ?? $productIds[$tree->id] ?? null;
	}

	public function getManualProduct(): LazyValue|null
	{
		if ($this->cf->settings?->manualProduct ?? false) {
			return $this->cf->settings?->manualProduct;
		}

		return null;
	}

}
