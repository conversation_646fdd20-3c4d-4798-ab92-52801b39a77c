<?php

declare(strict_types = 1);

namespace App\PostType\Rating\Actions;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Rating\Model\Orm\RatingLocalization;

class AttachProductsWithRatingLocalization
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly Repository $elasticProductRepository,
		private readonly TreeRepository $treeRepository,
	)
	{
	}

	public function handle(RatingLocalization $ratingLocalization): array
	{
		$productsArray = [];

		if ($ratingLocalization->tree !== null && $ratingLocalization->getManualProduct() === null) {
			$allChildren = $this->getAllTreeChildren($ratingLocalization->tree);

			$allChildren = array_merge($allChildren, [$ratingLocalization->tree]);

			foreach ($allChildren as $pathItem) {
				assert($pathItem instanceof CatalogTree);
				$product = $this->getProductInCategory($pathItem, $ratingLocalization);
				if ($product !== null) {
					$productsArray[$pathItem->id] = $product->id;
				}
			}
		}

		return $productsArray;
	}

	private function getProductInCategory(CatalogTree $tree, RatingLocalization $ratingLocalization): Product|null
	{
		$productIds = [];

		$productResults = $this->elasticProductRepository->searchByFilter($ratingLocalization->mutation, $tree, [], 1000);

		foreach ($productResults->getResults() as $result) {

			if (!isset($result->getSource()['productId'])) {
				continue;
			}

			$productIds[] = $result->getSource()['productId'];
		}

		$products = $this->productRepository->findByIds($productIds)->orderBy('soldCount', 'DESC');

		foreach ($products as $product) {
			$product->setMutation($ratingLocalization->mutation);
			$price = $product->priceVat($ratingLocalization->mutation, $ratingLocalization->mutation->getDefaultPriceLevel(), $ratingLocalization->mutation->getFirstState());

			if (! $product->isCourse() && (($ratingLocalization->cf->settings?->inStock ?? false) && ! $product->isInStock)) {
				continue;
			}

			if ($price->isGreaterThanOrEqualTo($ratingLocalization->cf->settings?->priceFrom ?? 0) && $price->isLessThan($ratingLocalization->cf->settings?->priceTo ?? 10000000)) {
				return $product;
			}
		}

		return null;
	}

	private function getAllTreeChildren(CatalogTree $tree): array
	{
		$children = [];

		$trees = $this->treeRepository->getAllDescendant($tree)->fetchAll();

		foreach ($trees as $child) {
			if ($child instanceof CatalogTree) {
				$children[] = $child;
			}
		}

		return $children;
	}

}
