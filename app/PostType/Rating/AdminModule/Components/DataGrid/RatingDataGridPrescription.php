<?php

declare(strict_types = 1);

namespace App\PostType\Rating\AdminModule\Components\DataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use Ublaboo\DataGrid\DataGrid;

readonly class RatingDataGridPrescription
{

	public function __construct()
	{
	}

	public function get(): DataGridDefinition
	{
		$datagrid = new DataGrid();
		$datagrid->setSortable();
		$datagrid->setSortableHandler('ratingSort!');

		return new DataGridDefinition(
			dataGrid: $datagrid,
		);
	}

}
