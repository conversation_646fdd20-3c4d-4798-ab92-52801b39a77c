<?php declare(strict_types=1);

namespace App\PostType\Rating\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Rating\AdminModule\Components\Form\FormData\RatingFormData;
use App\PostType\Rating\Model\Orm\RatingLocalization;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use App\PostType\Rating\Actions\AttachProductsWithRatingLocalization;
use Nette\Application\UI\Form;

class RatingFormPrescription
{

	public function __construct(
		private readonly RelationInfoFactory $relationInfoFactory,
		private readonly Builder $coreBuilder,
		private readonly Handler $coreHandler,
		private readonly SuggestUrls $urls,
		private readonly AttachProductsWithRatingLocalization $attachProductsWithRatingLocalization,
		private readonly string $coreFormPath,
	)
	{
	}

	public function get(RatingLocalization $ratingLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addCategory($ratingLocalization);
		$extenders[] = $this->addProducts($ratingLocalization);

		$form = new Form();
		$form->setMappedType(RatingFormData::class);
		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	private function addCategory(RatingLocalization $ratingLocalization): CustomFormExtender
	{
		$url = $this->urls['searchMutationPage'];
		$url->params['mutationId'] = $ratingLocalization->mutation->id;
		$url->params['templates'] = [':Front:Catalog:default'];

		$categoriesRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $ratingLocalization,
			propertyName: 'tree',
			suggestUrl: $url,
			inputSuggestPropertyName: 'name',
			toggleName: 'category',
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
				$this->coreBuilder->addHasOneRelation($form, $categoriesRelationsInfo);
			},
			successHandler: function (Form $form, RatingFormData $data) use ($categoriesRelationsInfo) {
				$this->coreHandler->handleHasOneRelation($data->tree->id, $categoriesRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION
				),
			]
		);
	}

	private function addProducts(RatingLocalization $ratingLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) {
			},
			successHandler: function (Form $form, RatingFormData $data) use ($ratingLocalization) {
				$ratingLocalization->treeProductIds = $this->attachProductsWithRatingLocalization->handle($ratingLocalization);
			}
		);
	}

}
