<?php

namespace App\PostType\Rating\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\Rating\AdminModule\Components\DataGrid\RatingDataGridPrescription;
use App\PostType\Rating\AdminModule\Components\Form\RatingFormPrescription;
use App\PostType\Rating\Model\Orm\RatingLocalization;
use App\PostType\Rating\Model\RatingLocalizationFacade;

final class RatingPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'rating';

	private RatingLocalization $ratingLocalization;

	private array $flashes = [];

	public function __construct(
		private DataGridFactory $dataGridFactory,
		private FormFactory $formFactory,
		private ShellFormFactory $shellFormFactory,
		private RatingLocalizationFacade $ratingLocalizationFacade,
		private RatingFormPrescription $ratingFormPrescription,
		private RatingDataGridPrescription $ratingDataGridPrescription,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$ratingLocalization = $this->orm->ratingLocalization->getById($id);
		if ($ratingLocalization === null) {
			$this->redirect('default');
		}

		$this->ratingLocalization = $ratingLocalization;
	}


	public function renderEdit(): void
	{
		// dd($this->ratingLocalization->findProducts());
		$ratingRanges = [];
		$ratings = $this->orm->ratingLocalization->findBy([
			'tree' => $this->ratingLocalization->tree,
		]);

		foreach ($ratings as $rating) {
			$ratingRanges[] = [
				'from' => $rating->cf->settings?->priceFrom ?? 0,
				'to' => $rating->cf->settings?->priceTo ?? 0,
			];
		}

		if ($this->hasCollisions($ratingRanges)) {
			$this->setFlashMessage('price_ranges_collision', 'error');
		}
	}

	public function setFlashMessage(string $message, string $type = 'info'): void
	{
		$this->flashes[] = (object) [
			'message' => $message,
			'type' => $type,
		];
	}

	public function getFlashMessages(): array
	{
		return $this->flashes;
	}

	private function hasCollisions(array $ranges): bool
	{
		for ($i = 0; $i < count($ranges); $i++) {
			$fromA = (float) $ranges[$i]['from'];
			$toA = (float) $ranges[$i]['to'];

			if ($fromA === 0.0 && $toA === 0.0) {
				continue;
			}

			for ($j = $i + 1; $j < count($ranges); $j++) {
				$fromB = (float) $ranges[$j]['from'];
				$toB = (float) $ranges[$j]['to'];

				if ($fromB === 0.0 && $toB === 0.0) {
					continue;
				}

				if ($fromA <= $toB && $fromB <= $toA) {

					return true;
				}
			}
		}
		return false;
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(
			self::ORM_REPOSITORY_NAME,
			$this->orm->ratingLocalization->findAll()->orderBy('sort', 'ASC'),
			$this->ratingDataGridPrescription->get()
		);
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->formFactory->create($this->ratingLocalizationFacade, $this->ratingLocalization, $userEntity, $this->ratingFormPrescription->get($this->ratingLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, facade: $this->ratingLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

	public function handleRatingSort(): void
	{
		$query = $this->getHttpRequest()->getQuery();

		$item_id = (int) $query['grid-item_id'];
		$prev_id = (int) ($query['grid-prev_id'] ?? null);
		$next_id = (int) ($query['grid-next_id'] ?? null);

		if (($item_id + $prev_id + $next_id) >= 3) {
			$this->orm->ratingLocalization->initSort();

			$item = $this->orm->ratingLocalization->getById($item_id);
			$prev = $this->orm->ratingLocalization->getById($prev_id);
			$next = $this->orm->ratingLocalization->getById($next_id);

			$this->orm->ratingLocalization->moveUp($prev?->sort ?? 0, $item->sort);
			$this->orm->ratingLocalization->moveDown($next?->sort ?? 0, $item->sort);

			$this->orm->refreshAll();

			if ($prev !== null) {
				$item->sort = $prev->sort + 1;
			} elseif ($next !== null) {
				$item->sort = (($nordr = ($next->sort - 1)) > 0) ? $nordr : 1;
			} else {
				$item->sort = 1;
			}

			$this->orm->ratingLocalization->persistAndFlush($item);

		}

		if ($this->isAjax()) {
			$this->getPayload()->newUrl = $this->getHttpRequest()->getUrl()->withQuery([]);
			$this->sendPayload();
		}
	}

}
