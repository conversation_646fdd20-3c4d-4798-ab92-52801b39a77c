cf:
	templates:
		ratingLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					image:
						type: image # has size
						label: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (min. 1920x823)"
					color:
						type: select
						label: "Barva"
						defaultValue: 'green'
						options: [
							{ label: "Zele<PERSON>", value: "green" },
							{ label: "<PERSON><PERSON><PERSON>", value: "yellow" },
							{ label: "<PERSON><PERSON><PERSON>", value: "red" },
							{ label: "Modrá", value: "blue" },
						]
					inStock:
						type: checkbox
						label: "Jen produkty skladem"
						defaultValue: true
					priceFrom:
						type: text
						label: "Minimální cena"
					priceTo:
						type: text
						label: "Maximální cena"
					manualProduct:
						type: suggest
						label: "Produkt"
						url: @cf.suggestUrls.searchProduct
						subType: product
						

cc:
	templates:
	# 	rating: []
	# 	ratingLocalization: []

application:
	mapping:
		Rating: App\PostType\Rating\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Rating: rating

services:
	- App\PostType\Rating\AdminModule\Components\Form\RatingFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Rating\Model\RatingLocalizationFacade
	- App\PostType\Rating\Model\Messanger\RatingFindProductConsumer
	- App\PostType\Rating\AdminModule\Components\DataGrid\RatingDataGridPrescription
	- App\PostType\Rating\Actions\AttachProductsWithRatingLocalization

messenger:
	routing:
		App\PostType\Rating\Model\Messanger\RatingFindProductMessage: [defaultFront]

	
