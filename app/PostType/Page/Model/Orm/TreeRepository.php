<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\FindPairs;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasPublicParameter;

/**
 * @method ICollection<Tree> findLastInPath($id)
 * @method ICollection<Tree> findInPath($id)
 * @method ICollection<Tree> searchByName(string $q, ?int $parentId = null, ?int $pathId = null, ?array $excluded = null)
 * @method ICollection<Tree> findFilteredPages(array $pageIds)
 *
 * @method ICollection<Tree> findMainTreesInRelations(Tree $attachedTree, string $type)
 * @method ICollection<Tree> findAttachedTreesInRelations(Tree $mainTree, string $type)
 *
 * @method ICollection<Tree> addParameterValue($tree, $parameterValue)
 * @method ICollection<Tree> removeParameterValue($tree, $parameterValue)
 * @method ICollection<Tree> removeParameter($tree, $parameter)
 * @method ICollection<Tree> findByParameterValueIds($ids)
 *
 * @method Tree getLanguageParent(Tree $tree)
 *
 * @method Result findAllIds(?int $limit)
 * @method array findChildIds(array $pathIds)
 *
 * @method ICollection<Tree> findTreesInTreeProductRelations(Product $product, string $type, Mutation $mutation)
 * @method ICollection<Tree> findTreesInProductTreeRelations(Product $product, Mutation $mutation)
 *
 * @method Tree|null getById($id)
 *
 * @method ICollection<Tree> findByExactOrder(array $ids, string $columnName = 'id')
 * @method ICollection<Tree> findWithFaqLocalization(array $ids)
 * @extends Repository<Tree>
 */
final class TreeRepository extends Repository implements CollectionById, QueryForIdsByMutation, FindPairs
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [Tree::class, CatalogTree::class, CommonTree::class];
	}


	public function getEntityClassName(array $data): string
	{
		return match ($data['type']) {
			Tree::TYPE_CATALOG => CatalogTree::class,
			default => CommonTree::class,
		};
	}

	public function getByExtId(int|string $id): ?Tree
	{
		return $this->getBy(['extId' => (string) $id]);
	}

	public function getByUidClear(string $uid): ?Tree
	{
		$cond = [
			'uid' => $uid,
		];
		return $this->getBy($cond);
	}


	public function getByUid(string $uid, Mutation $mutation): ?Tree
	{
		$cond = [
			'uid' => $uid,
			'mutation' => $mutation,
		];
		$cond = array_merge($cond, $this->getPublicOnlyWhere());

		return $this->getBy($cond);
	}


	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findFilteredPages($ids);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof TreeMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	public function findPairs(string $value, ?string $key = null, ?array $where = [], ?array $order = []): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof TreeMapper);
		return $mapper->findPairs($value, $key, $where, $order);
	}

	public function findPairsInPath(int $pathId, string $id, string $value, ?array $order = []): Result
	{
		return $this->findPairs($value, $id, [TreeMapper::getLikePath($pathId)], $order);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findWithPublicFaqLocalization(array $ids): ICollection
	{
		return $this->findBy([
			'id' => $ids,
			'faqLocalizations->public' => 1,
		]);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function getAllDescendant(Tree $tree): ICollection
	{
		return $this->findInPath($tree->id);
	}

}
