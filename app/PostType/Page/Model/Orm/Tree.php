<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use App\Model\ConfigService;
use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\JsonArrayContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ConvertableToTree;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasFormDefaultData;
use App\Model\Orm\Traits\HasTemplateCache;
use App\Model\Orm\TreeProduct\TreeProduct;
use App\Model\Orm\TreeTree\TreeTree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationTree;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;
use App\PostType\Rating\Model\Orm\RatingLocalization;
use App\PostType\Faq\Model\Orm\Localization\FaqLocalization;

/**
 * @property string|null $pathString
 * @property string $type {enum self::TYPE_*} {default self::TYPE_COMMON}
 * @property int $last
 * @property int $rootId
 * @property string|null $extId
 * @property int|null $level
 * @property int|null $sort
 * @property bool|null $public {default false}
 * @property int|null $created
 * @property int|null $edited
 * @property bool $forceNoIndex {default false}
 * @property bool $hideInSearch {default false}
 * @property bool $hideInMenu {default false}
 * @property string $uid {default ''}
 * @property string $name {default ''}
 * @property string $nameTitle {default ''}
 * @property string $nameAnchor {default ''}
 * @property string $nameAnchorBreadcrumb {default ''}
 * @property string $nameShort {default ''}
 * @property string $nameHeading {default ''}
 * @property string $annotation {default ''}
 * @property float $score {default 0}
 * @property string $description {default ''}
 * @property string $keywords {default ''}
 * @property string $content {default ''}
 * @property DateTimeImmutable $createdTime {default 'now'}
 * @property DateTimeImmutable $createdTimeOrder {default 'now'}
 * @property DateTimeImmutable|null $editedTime
 * @property DateTimeImmutable $publicFrom {default 'now'}
 * @property DateTimeImmutable $publicTo {default '+100 year'}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 * @property string|null $seoTitleFilter
 * @property string|null $seoAnnotationFilter
 * @property string|null $seoDescriptionFilter
 *
 * RELATIONS
 * @property OneHasMany<Tree> $crossroadAll {1:m Tree::$parent, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property OneHasMany<FaqLocalization> $faqLocalizations {1:m FaqLocalization::$tree, cascade=[persist, remove]}
 *
 * @property OneHasMany<ProductTree> $productTrees {1:m ProductTree::$tree, orderBy=[id=ASC], cascade=[persist, remove]}
 *
 * @property Tree|null $parent {m:1 Tree::$crossroadAll}
 * @property TreeParent|null $treeParent {m:1 TreeParent::$localizations}
 * @property Mutation $mutation  {m:1 Mutation::$trees}
 *
 * @property OneHasMany<TreeProduct> $treeProducts {1:m TreeProduct::$tree, cascade=[persist, remove]}
 * @property OneHasMany<TreeTree>|null $treeTrees {1:m TreeTree::$mainTree, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property OneHasMany<TreeTree>|null $attachedTreeTrees {1:m TreeTree::$attachedTree, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property OneHasMany<BlogLocalizationTree> $blogLocalizationTrees {1:m BlogLocalizationTree::$tree, cascade=[persist, remove]}
 * @property OneHasMany<RatingLocalization> $ratingLocalizations {1:m RatingLocalization::$tree}
 *
 *
 * VIRTUAL
 * @property array $path {virtual}
 * @property array $pathItems {virtual}
 *
 * @property ICollection<Tree> $crossroad {virtual}
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property string|null $firstImage {virtual}
 * @x_property-read ICollection $images {virtual}
 * @x_property-read ICollection $videos {virtual}
 * @x_property-read ICollection $links {virtual}
 * @x_property-read ICollection $files {virtual}
 * @property string $childsId {virtual}
 * @property ICollection<Tree> $crossroadItems {virtual}
 * @property ICollection<Tree> $crossroadItemsNoSort {virtual}
 *
 * @property ICollection<Product> $products {virtual}
 * @property ICollection<Product> $productsAll {virtual}
 *
 * @property ICollection<Tree> $pages {virtual}
 * @property ICollection<Tree> $pagesAll {virtual}
 *
 * @property int|null $parentId {virtual}
 * @property Tree|null $author {virtual}
 * @property string|null $items {virtual}
 * @x_property Tree|null $prev {virtual}
 * @x_property Tree|null $next {virtual}
 * @property array|null $paramLinks {virtual}
 * @property-read array $possibleTemplates {virtual}
 * @property-read array $templates {virtual}
 * @property-read bool $hasBadTemplate {virtual}
 * @property-read string|null $publicToString {virtual}
 * @property-read string|null $publicFromString {virtual}
 * @property-read string|null $createdTimeString {virtual}
 * @property-read int $productSaleCount {virtual}
 */
abstract class Tree extends RoutableEntity implements LocalizationEntity, HasImages, ConvertableToTree, Publishable, Synchronizable
{

	use HasFormDefaultData;
	use HasTemplateCache, HasCustomFields {
		HasCustomFields::onAfterPersist as private hasCustomFieldsOnAfterPersist;
		HasTemplateCache::onAfterPersist as private hasTemplateCacheOnAfterPersist;
		HasTemplateCache::onBeforeRemove as private hasTemplateCacheOnAfterRemove;
	}
	use HasCustomContent;

	public const TYPE_COMMON = 'common';
	public const TYPE_CATALOG = 'catalog';

	public const UID_DISCOUNT = 'discount';
	public const UID_MENU_MAIN = 'menuMain';
	public const UID_ESHOP = 'eshop';
	public const UID_TITLE = 'title';
	public const UID_MY_REVIEWS = 'myReviews';
	public const UID_SEARCH = 'search';
	public const UID_CART = 'cart';
	public const UID_ORDER_STEP_1 = 'step1';
	public const UID_ORDER_STEP_2 = 'step2';
	public const UID_ORDER_STEP_3 = 'step3';
	public const UID_SHARED_LIBRARY = 'sharedLibrary';
	public const CACHED_LIST_TAG = 'listTag';

	private UserRepository $userRepository;

	private ConfigService $configService;

	protected TreeRepository $treeRepository;

	protected BlogLocalizationRepository $blogLocalizationRepository;

	protected ProductRepository $productRepository;

	protected TreeModel $treeModel;

	public function injectService(
		ConfigService $configService,
		TreeRepository $treeRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		ProductRepository $productRepository,
		UserRepository $userRepository,
		TreeModel $treeModel,
	): void
	{
		$this->configService = $configService;
		$this->userRepository = $userRepository;
		$this->treeRepository = $treeRepository;
		$this->blogLocalizationRepository = $blogLocalizationRepository;
		$this->productRepository = $productRepository;
		$this->treeModel = $treeModel;
	}


	protected function getterPath(): array
	{
		if (!isset($this->id) || $this->pathString === null) {
			return [];
		}

		$path = preg_split('~\|~', $this->pathString, -1, PREG_SPLIT_NO_EMPTY);
		assert($path !== false);

		$path = array_map('intval', $path);
		return $path;
	}


	protected function setterPath(array $path = []): void
	{
		if (count($path) > 0) {
			$this->pathString = implode('|', $path) . '|';
		} else {
			$this->pathString = null;
		}
	}


	protected function getterFirstImage(): ImageEntity|null
	{
		return isset($this->cf->settings->mainImage) ? $this->cf->settings->mainImage->getEntity() : null;
	}

	protected function getterImages(): ICollection // @phpstan-ignore-line
	{
		return new EmptyCollection();
	}

	protected function getterVideos(): ICollection // @phpstan-ignore-line
	{
		return new EmptyCollection();
	}

	protected function getterFiles(): ICollection // @phpstan-ignore-line
	{
		return new EmptyCollection();
	}

	protected function getterLinks(): ICollection // @phpstan-ignore-line
	{
		return new EmptyCollection();
	}


	protected function getterPublicFromString(): string
	{
		return $this->publicFrom->format('Y-m-d H:i:s');
	}


	protected function getterCreatedTimeString(): string
	{
		return $this->createdTimeOrder->format('Y-m-d H:i:s');
	}


	protected function getterPublicToString(): string
	{
		return $this->publicTo->format('Y-m-d H:i:s');
	}


	protected function getterParentId(): ?int
	{
		if ($this->parent !== null) {
			return $this->parent->id;
		} else {
			return null;
		}
	}


	public function getCreatedBy(): ?User
	{
		return $this->userRepository->getById($this->created);
	}


	public function getEditedBy(): ?User
	{
		return $this->userRepository->getById($this->edited);
	}


	protected function getterChildsId(): array
	{
		$ret = [];
		foreach ($this->crossroadItems as $child) {
			$ret[] = $child->id;
		}

		return $ret;
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterCrossroadItems(): ICollection
	{
		if (!isset($this->cache['getterCrossroadItems'])) {
			$this->cache['getterCrossroadItems'] = $this->treeRepository->findLastInPath($this->id)->orderBy('sort')->findBy($this->treeRepository->getPublicOnlyWhere());
		}

		return $this->cache['getterCrossroadItems'];
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterCrossroadItemsNoSort(): ICollection
	{
		if (!isset($this->cache['getterCrossroadItemsNoSort'])) {
			$this->cache['getterCrossroadItemsNoSort'] = $this->treeRepository->findLastInPath($this->id)->findBy($this->treeRepository->getPublicOnlyWhere());
		}

		return $this->cache['getterCrossroadItemsNoSort'];
	}


	protected function getterPrev(): ?Tree
	{
		$prev = null;
		if ($this->isPersisted() && $this->parent !== null) {
			foreach ($this->getSiblings() as $item) {
				if ($item->id !== $this->id) {
					$prev = $item;
				}

				if ($item->id === $this->id) {
					break;
				}
			}
		}

		return $prev;
	}


	protected function getterNext(): ?Tree
	{
		$next = null;
		if ($this->isPersisted() && $this->parent !== null) {
			$thisIsFound = false;
			foreach ($this->getSiblings() as $item) {
				if ($thisIsFound) {
					$next = $item;
					break;
				}

				if ($item->id === $this->id) {
					$thisIsFound = true;
				}
			}
		}

		return $next;
	}


	/**
	 * @return ICollection<Tree>
	 */
	private function getSiblings(): ICollection
	{
		if ($this->isPersisted() && $this->parent !== null) {
			return $this->parent->crossroad;
		} else {
			/** @var ICollection<Tree> $ret */
			$ret = new EmptyCollection();
			return $ret;
		}
	}



	public function getRootId(): string
	{
		if ($this->getRawValue('pathString') === null || $this->getRawValue('pathString') === '') {
			return (string) $this->id;
		}

		$path = $this->path;
		return $path[0];
	}


	public function getRootLang(): string
	{
		return $this->mutation->langCode;
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterPages(): ICollection
	{
		return $this->pagesAll->findBy($this->treeRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterPagesAll(): ICollection
	{
		return $this->treeRepository->findAttachedTreesInRelations($this, TreeTree::TYPE_NORMAL);
	}


	// u clanku - produkty zminene v clanku - jen s cenou

	/**
	 * @return ICollection<Product>
	 */
	protected function getterProducts(): ICollection
	{
		return $this->productsAll->findBy($this->productRepository->getPublicOnlyWhere());
	}

	// u clanku - produkty zminene v clanku

	/**
	 * @return ICollection<Product>
	 */
	protected function getterProductsAll(): ICollection
	{
		return $this->productRepository->findProductsInTreeProductRelations($this, TreeProduct::TYPE_NORMAL_TO_TREE);
	}

	protected function getterProductSaleCount(): int
	{
		return $this->productRepository->getTreeSoldProductsCount($this);
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterCrossroad(): ICollection
	{
		if (!isset($this->cache['crossroad'])) {
			$this->cache['crossroad'] = $this->crossroadAll->toCollection()->findBy($this->treeRepository->getPublicOnlyWhere());
		}

		return $this->cache['crossroad'];
	}


	protected function getterPossibleTemplates(): array
	{
		$ret = $this->templates;

		if (!in_array($this->template, $ret)) {
			// pokud nahodou dojde k tomu ze entita ma jinaci tempalte než je pomovoleny
			// je nutne tento template pridat (aby naspadl formular na povolenych hodnotach atributu template)
			$ret[$this->template] = $this->configService->get('templates', $this->template);
		}

		return $ret;
	}


	protected function getterTemplates(): array
	{
		$ret = $this->configService->get('templates');

		if ($this->isPersisted() && $this->parent) {
			// pokud je entita ulozena a ma rodice
			// prekontroluje pole predpisu pro tempaltes

			$templateNames = $this->configService->get('templatesParentsRules', $this->parent->template);

			if ($templateNames && is_array($templateNames)) {
				// podle klice vyfiltruj jen ty pouzitelne
				$ret = array_filter($ret, function ($value, $key) use ($templateNames) {
					return in_array($key, $templateNames);
				}, ARRAY_FILTER_USE_BOTH);
			}
		}

		return $ret;
	}


	protected function getterHasBadTemplate(): bool
	{
		return !isset($this->templates[$this->template]);
	}

	protected function getterPathItems(): array
	{
		if (!isset($this->cache['pathItems'])) {
			$this->cache['pathItems'] = $this->treeRepository->findByIdOrder($this->path)->findBy($this->treeRepository->getPublicOnlyWhere())->fetchAll();
		}

		return $this->cache['pathItems'];
	}


	public function getPathSentence(string $sep = '>', int $fromLevel = -1): string
	{
		$parts = [];
		foreach ($this->pathItems as $pathItem) {
			if ($pathItem->level > $fromLevel) {
				$parts[] = $pathItem->name;
			}
		}
		$sep = ' ' . $sep . ' ';
		return implode($sep, $parts);
	}

	public function getPathSentenceWithMyself(string $sep = '>', int $fromLevel = -1): string
	{
		$prefix = $this->getPathSentence($sep, $fromLevel);
		$parts = [];
		if ($prefix) {
			$parts[] = $prefix;
		}

		$sep = ' ' . $sep . ' ';
		$parts[] = $this->name;
		return implode($sep, $parts);
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function isPublic(): bool
	{
		$now = new DateTimeImmutable();
		return $this->public && $this->publicTo > $now && $this->publicFrom < $now;
	}

	public function getId(): int
	{
		return $this->id;
	}


	public function getParent(): ?ParentEntity
	{
		return $this->treeParent;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof TreeParent);
		$this->treeParent = $parentEntity;
	}


	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->cf->base->mainImage) ? $this->cf->base->mainImage->getEntity() : null;
	}


	public function getTreeItems(): array
	{
		return $this->crossroadAll->toCollection()->fetchAll();
	}


	/**
	 * @return OneHasMany<Tree>
	 */
	public function getItemsRelation(): OneHasMany
	{
		return $this->crossroadAll;
	}

	public function getTreeSiblings(): array
	{
		$parent = $this->parent;
		if ($parent !== null) {
			$siblings = $parent->crossroad->fetchAll();
		} else {
			$siblings = $this->getRepository()->findBy(['parent' => null])->orderBy('sort')->fetchAll();
		}

		return $siblings;
	}


	public function getParentNode(): ?ConvertableToTree
	{
		return $this->parent;
	}


	public function setParentNode(?ConvertableToTree $parent): void
	{
		assert($parent instanceof Tree);
		$this->parent = $parent;
	}


	public function setLeafParameter(bool $isLeaf): void
	{
		$this->last = (int) $isLeaf;
	}

	public function setSort(int $sort): void
	{
		$this->sort = $sort;
	}

	public function setLevel(int $level): void
	{
		$this->level = $level;
	}

	public function getLevel(): int
	{
		return $this->level;
	}

	public function setPathNodes(array $items): void
	{
		$this->path = array_map(function (ConvertableToTree $item) {
			return $item->getId();
		}, $items);
	}

	public function getPathNodes(): array
	{
		return $this->pathItems;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getIsPublic(): bool
	{
		return (bool) $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getTitle(): string
	{
		if (strlen(trim($this->nameHeading)) !== 0) {
			return $this->nameHeading;
		}
		return $this->name;
	}

	public function getNameAnchorBreadcrumb(): string
	{
		if (strlen(trim($this->nameAnchorBreadcrumb)) !== 0) {
			return $this->nameAnchorBreadcrumb;
		}

		if (strlen(trim($this->nameAnchor)) !== 0) {
			return $this->nameAnchor;
		}

		return $this->name;
	}

	public function onAfterPersist(): void
	{
		$this->hasCustomFieldsOnAfterPersist();
		$this->hasTemplateCacheOnAfterPersist();
	}
	public function onBeforeRemove(): void
	{
		$this->hasTemplateCacheOnAfterRemove();
	}

	public function getTemplateCacheTagsCascade(): array
	{
		return [MenuMainLocalization::class, self::CACHED_LIST_TAG];
	}

	public function getExternalId(): null|string
	{
		return $this->extId;
	}

	public function setExternalId(null|string|int $externalId): void
	{
		$this->extId = (string) $externalId;
	}

}
