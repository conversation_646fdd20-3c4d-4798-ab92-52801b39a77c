{varType App\PostType\Blog\Model\Orm\BlogLocalization $blogLocalization}

{if isset($blogLocalization->firstImage) && $blogLocalization->firstImage !== null}
	{php $img = $blogLocalization->firstImage->getSize('md')}
	{php $stDataImage = $mutation->getBaseUrl().$img->src}
	{php $stDataImageW = $img->width}
	{php $stDataImageH = $img->height}
{else}
	{php $stDataImage = null}
{/if}

<script type="application/ld+json">
	{
		"@context": "http://schema.org/",
		"@type": "NewsArticle",
		"mainEntityOfPage": {$mutation->getBaseUrlWithPrefix()."/".$blogLocalization->alias},
		"headline": {$blogLocalization->name},
		{if $blogLocalization->publicFrom !== null}
			"datePublished": {$blogLocalization->publicFrom->format('Y-m-d')},
		{/if}
		{if $blogLocalization->editedTime !== null}
			"dateModified": {$blogLocalization->editedTime->format('Y-m-d')}
		{else}
			"dateModified": {$blogLocalization->createdTimeOrder->format('Y-m-d')}
		{/if}
		"description": {$blogLocalization->annotation},
		{if $stDataImage !== null}
			"image": {
				"@type": "ImageObject",
				"height": {$stDataImageW},
				"width": {$stDataImageH},
				"url": {$stDataImage}
			},
		{/if}
		{if $author !== null}
			"author": {
				"@type": "Person",
				"name": {$author->name}
			},
		{/if}
		"publisher": {
			"@type": "Organization",
			"logo": {
				"@type": "ImageObject",
				"url": {$mutation->getBaseUrl()."/static/img/logo.png"}
			},
			"name": {$publisher}
		},
		"articleBody": {$content}
	}
	</script>
