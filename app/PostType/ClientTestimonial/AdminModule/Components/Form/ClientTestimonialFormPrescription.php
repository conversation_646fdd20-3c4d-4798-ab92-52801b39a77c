<?php

declare(strict_types=1);

namespace App\PostType\ClientTestimonial\AdminModule\Components\Form;

use App\PostType\ClientTestimonial\AdminModule\Components\Form\FormData\ClientTestimonialFormData;
use App\PostType\ClientTestimonial\Model\Orm\Localization\ClientTestimonialLocalization;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use Nette\Application\UI\Form;

class ClientTestimonialFormPrescription
{

	public function get(ClientTestimonialLocalization $clientTestimonialLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addSort($clientTestimonialLocalization);
		$extenders[] = $this->changeNameLabel();

		$form = new Form();
		$form->setMappedType(ClientTestimonialFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	private function changeNameLabel(): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) {
				$form->getComponent('localization')
					->getComponent('name')
					->setCaption('Jméno a příjmení');
			},
			successHandler: function (Form $form, ClientTestimonialFormData $formData) {
			},
		);
	}

	private function addSort(ClientTestimonialLocalization $clientTestimonialLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($clientTestimonialLocalization) {
				$form->addInteger('sort', 'sort')
					->setRequired()
					->setDefaultValue($clientTestimonialLocalization->sort ?? 0);
			},
			successHandler: function (Form $form, ClientTestimonialFormData $formData) use ($clientTestimonialLocalization) {
				$clientTestimonialLocalization->sort = $formData->sort;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/../templates/settings.latte',
					type: CommonTemplatePart::TYPE_SIDE,
				),
			],
		);
	}

}
