<?php

declare(strict_types = 1);

namespace App\PostType\ClientTestimonial\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use Nextras\Orm\Repository\Repository;
use App\Support\Orm\Traits\Repository\SerchableRepository;
use Nextras\Orm\Collection\ICollection;

/**
 * @method ICollection<ClientTestimonial> findByExactOrder(array $ids)
 * @method ClientTestimonial getById($id)
 * @extends Repository<ClientTestimonial>
 */
final class ClientTestimonialRepository extends Repository implements Searchable, CollectionById
{

	/** @phpstan-use SerchableRepository<ClientTestimonial> */
	use SerchableRepository;

	public static function getEntityClassNames(): array
	{
		return [ClientTestimonial::class];
	}

	/**
	 * @return ICollection<ClientTestimonial>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
