<?php

declare(strict_types = 1);

namespace App\PostType\ClientTestimonial\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use App\Support\Orm\Contracts\Mapper\SearchableMapperContract;
use App\Support\Orm\Traits\Mapper\SearchableMapper;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Collection\ICollection;

/**
 * @extends DbalMapper<ClientTestimonial>
 * @implements SearchableMapperContract<ClientTestimonial>
 */
class ClientTestimonialMapper extends DbalMapper implements SearchableMapperContract
{

	use HasCamelCase;
	/** @phpstan-use SearchableMapper<ClientTestimonial> */
	use SearchableMapper;

	protected array $searchableFields = ['internalName'];

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'client_testimonial';
	}

	/**
	 * @return ICollection<ClientTestimonial>
	 */
	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

}
