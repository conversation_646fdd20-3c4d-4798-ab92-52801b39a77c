parameters:
	cs-mutation-code: \App\Model\Orm\Mutation\Mutation::CODE_CS

extensions:
	cf: App\Model\CustomField\CustomFieldsExtension

cf:
	definitions:
		content:
			type: tinymce
			label: "Obsah"
		photo:
			type: image
			label: "Fotka"
		photos:
			type: image
			label: "Fotky"
			multiple: true
		files:
			type: file
			label: "Soubory"
			multiple: true
		usp_list:
			type: list
			label: "USP"
			items:
				text:
					type: text
		categories:
			type: list
			label: "Kategorie"
			items:
				link:
					extends: @cf.definitions.linkChoose
					label: "Stránka / odkaz"
				items:
					type: list
					label: "Podkategorie"
					items:
						link:
							extends: @cf.definitions.linkChoose
				desc:
					type: textarea
					label: "Popis"
				# image:
				# 	type: image # has size
				# 	label: "Obrázek nebo SVG (rozcestník a submenu, min. 420x315)"
				bg:
					type: select
					label: "Zvýraznění"
					options: [
						{ label: "Žlutá", value: "u-bgc-alert-light" },
						{ label: "<PERSON><PERSON>ech<PERSON>", value: "u-bgc-violet-gradient" },
					]
		tocName:
			type: text
			label: "Text kotvy v obsahu"
			translatable: true
		crossroad:
			type: group
			label: "Rozcestník"
			items:
				items:
					type: list
					label: "Položky"
					items:
						link:
							extends: @cf.definitions.linkChoose
							label: "Stránka"
						image:
							type: image # has size
							label: "Obrázek (nepovinné, přebírá se ze stránky, min. 126x96)"
						highlight:
							type: select
							label: "Zvýraznění"
							defaultValue: 'none'
							options: [
								{ label: "Žlutá", value: "u-bgc-alert-light" },
								{ label: "Šedá", value: 'u-bgc-default' }
								{ label: "Přechod", value: "u-bgc-violet-gradient" },
							]
		person_cta:
			type: group
			label: "CTA s osobou"
			items:
				person:
					type: image
					label: "Fotografie osoby"
				content:
					type: tinymce
					label: "Obsah"
		references:
			type: list
			label: "Reference"
			items:
				imgPerson:
					type: image # has size
					label: "Fotografie osoby (min. 150x150)"
				imgCompany:
					type: image # has size
					label: "Logo společnosti (min. 150x150)"
				text:
					type: textarea
					label: "Text"
				name:
					type: text
					label: "Jméno a příjmení"
				info:
					type: text
					label: "Pozice / společnost"
		flag:
			type: group
			label: "Štítek"
			items:
				text:
					type: text
					label: "Text"
				type:
					type: select
					label: "Typ"
					options: [
						{ label: "Světle zelený", value: "green-light" },
						{ label: "Světle modrý", value: "blue-light" },
						{ label: "Světle šedý", value: "gray-light" },
						{ label: "Světle fialový", value: "purple-light" },
						{ label: "Světle oranžový", value: "orange-light" },
					]

		faq:
			type: group
			label: "FAQ"
			items:
				title:
					type: text
					label: "Nadpis"
				btn:
					type: radio
					label: "Tlačítko pod výpisem"
					options: [
						{ label: "Žádné", value: false },
						{ label: "Odkaz na položení otázky (defaultní)", value: "ask" },
						{ label: "Odkaz na výpis", value: "more" },
					]
					defaultValue: 'ask'
				items:
					type: list
					label: "Otázky"
					items:
						question:
							type: text
							label: "Otázka"
						answer:
							type: textarea
							label: "Odpověď"
				faqCategory:
					type: suggest
					label: "Kategorie FAQ"
					subType: tree
					url: @cf.suggestUrls.searchFaqCategory

		header:
			type: group
			label: "Nastavení záhlaví"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 2880x630)"
				decor:
					type: image # has size
					label: "Dekorace v pravém rohu, pouze pokud není nahraný obrázek (min.920x60)"
				content:
					type: tinymce
					label: "Popis kategorie"
				extra:
					type: group
					hasContentToggle: true
					label: "Obsah v pravém sloupci"
					items:
						type:
							type: radio
							inline: true
							isContentToggle: true
							defaultValue: 'none'
							options: [
								{ label: "Žádný", value: "none" },
								{ label: "Video", value: "video" },
								{ label: "Odkazy", value: "links" },
							]
						video:
							type: group
							label: "Video"
							items:
								link:
									type: text
									label: "Odkaz na Youtube / Vimeo"
								image:
									type: image # has size
									label: "Zástupný obrázek (min. 645x362)"
						links:
							type: group
							label: "Odkazy"
							items:
								title:
									type: text
									label: "Nadpis"
								links:
									type: list
									label: "Odkazy"
									items:
										link:
											extends: @cf.definitions.linkChoose

		message:
			type: group
			label: 'Info lišta'
			items:
				emoji:
					label: "Emoji (https://getemoji.com/)"
					type: text
				bg:
					type: select
					label: "Barva pozadí"
					defaultValue: 'warning'
					options: [
						{ label: "Zelená", value: "ok" },
						{ label: "Žlutá (defaultní)", value: "warning" },
						{ label: "Oranžová", value: "warning2" },
						{ label: "Červená", value: "error" },
						{ label: "Fialová", value: "violet" },
					]

		person:
			type: group
			label: "Osoba"
			items:
				image:
					type: image # has size
					label: "Fotka (min. 320x320)"
				name:
					type: text
					label: "Jméno a příjmení"
				position:
					type: text
					label: "Pozice"

		benefits:
			type: list
			label: "Benefity"
			items:
				icon:
					type: image # has size
					label: "Ikona (min. 100x100)"
				text:
					type: textarea
					label: "Text"

		bnr:
			type: group
			label: "Banner"
			items:
				images:
					type: group
					label: "Fotografie"
					items:
						desktop:
							type: image # has size
							label: "Desktop (1400x180)"
						tablet:
							type: image # has size
							label: "Tablet (volitelné, 1000x143)"
						mobile:
							type: image # has size
							label: "Mobile (volitelné, 750x362)"
				link:
					extends: @cf.definitions.linkChoose
					label: "Odkaz (volitelné) a text tlačítka"

		linkChoose:
			hasContentToggle: true
			type: group
			label: "Odkaz"
			items:
				toggle:
					type: radio
					inline: true
					isContentToggle: true
					defaultValue: 'systemHref'
					options: [
						{ label: "Systémová stránka", value: "systemHref" },
						{ label: "Vlastní odkaz", value: "customHref" },
					]
				systemHref:
					type: group
					items:
						page:
							type: suggest
							label: "Stránka"
							subType: tree
							url: @cf.suggestUrls.searchMutationPage
						hrefName:
							type: text
							label: "Text odkazu (volitelné)"
				customHref:
					type: group
					items:
						href:
							type: text
							label: "Odkaz"
						hrefName:
							type: text
							label: "Text odkazu"

		two_columns:
			type: group
			label: "Obsah ve dvou sloupcích"
			items:
				left:
					type: group
					label: "Levý sloupec"
					items:
						title:
							type: text
							label: "Nadpis"
						content:
							extends: @cf.definitions.content
				right:
					type: group
					label: "Pravý sloupec"
					items:
						title:
							type: text
							label: "Nadpis"
						content:
							extends: @cf.definitions.content

		usp:
			type: group
			label: "USP"
			items:
				narrow:
					type: checkbox
					label: "Úzké zobrazení s nadpisem nahoře"
					defaultValue: false
				title:
					type: text
					label: "Nadpis"
				items:
					extends: @cf.definitions.usp_list
					label: "Body"

		bannerChoose:
			type: list
			items:
				banner:
					type: suggest
					subType: banner
					url: @cf.suggestUrls.searchBanner

	fields:
		mutationData:
			type: group
			label: "Nastavení mutace"
			items:
				ogImage:
					type: image # has size
					label: "og:image - obrázkový náhled webu (1200x630)"
				daysToPast:
					type: text
					subType: number
					label: "Počet dní nazpět pro výpočet statistiky prodaných kusů"

		userMenuLoggedUser:
			label: "User menu v hlavičce"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		serviceMenu:
			type: list
			label: "Servisní menu"
			items:
				link:
					extends: @cf.definitions.linkChoose
		parameterInPicker:
			type: group
			label: "Nastavení hodnoty pro výběr varianty"
			items:
				image:
					type: image
					label: "Obrázek"

		header:
			extends: @cf.definitions.header
			label: "Nastavení záhlaví"

		# register_benefits:
		# 	type: group
		# 	label: "Výhody registrace (hlavička)"
		# 	items:
		# 		content:
		# 			type: tinymce

		# homepage_aboutus:
		# 	type: group
		# 	label: "Sekce O nás"
		# 	items:
		# 		heading:
		# 			type: text
		# 			label: "Nadpis"
		# 		text:
		# 			type: tinymce
		# 			label: "Obsah"
		# 		person:
		# 			type: group
		# 			label: "Podpis"
		# 			items:
		# 				name:
		# 					type: text
		# 					label: "Jméno a příjmení"
		# 				image:
		# 					extends: @cf.definitions.photo # has size
		# 					type: image
		# 					label: "Fotka (min. 84x84)"
		# 				link:
		# 					type: group
		# 					label: "Odkaz"
		# 					items:
		# 						text:
		# 							type: text
		# 							label: "Text"
		# 						url:
		# 							type: suggest
		# 							subType: tree
		# 							label: "Stránka"
		# 							placeholder: "Zadejte URL přes našeptávač"
		# 							url: @cf.suggestUrls.searchMutationPage
		# 		images:
		# 			type: group
		# 			label: "Obrázky (min. 750x750)" # has size
		# 			items:
		# 				image1:
		# 					extends: @cf.definitions.photo
		# 				image2:
		# 					extends: @cf.definitions.photo
		# 				image3:
		# 					extends: @cf.definitions.photo
		# 				image4:
		# 					extends: @cf.definitions.photo
		# 				image5:
		# 					extends: @cf.definitions.photo
		# 				image6:
		# 					extends: @cf.definitions.photo
		# userMenuUnloggedUser:
		# 	label: "User menu pro nepřihlášeného (hlavička)"
		# 	type: list
		# 	items:
		# 		tree:
		# 			type: suggest
		# 			label: "Stránka"
		# 			subType: tree
		# 			url: @cf.suggestUrls.searchMutationPage

		deliveryPayment:
			label: "Nastavení dopravy a platby"
			type: group
			items:
				icon:
					type: image # has size
					multiple: true
					label: "Obrázek (min. 100x100) / SVG ikona"
				marker:
					type: image # has size
					multiple: false
					label: 'Obrázek (min. 100x100) / SVG ikona markeru na mapě pro výběr pobočky v mapě'
		paymentAccount:
			label: 'Nastavení bankovního spojení'
			type: group
			items:
				bban:
					type: text
					label: BBAN
				iban:
					type: text
					label: IBAN
				swift:
					type: text
					label: SWIFT / BIC
		# paymentText:
		# 	label: 'Nastavení stránky po odeslání objednávky'
		# 	type: group
		# 	items:
		# 		Physical:
		# 			type: group
		# 			label: 'Doručení na adresu'
		# 			items:
		# 				text:
		# 					type: tinymce
		# 					label: 'Text'
		# 				note:
		# 					type: textarea
		# 					label: 'Poznámka'

		# 		Pickup:
		# 			type: group
		# 			label: 'Odberné místo'
		# 			items:
		# 				text:
		# 					type: tinymce
		# 					label: 'Text'
		# 				note:
		# 					type: textarea
		# 					label: 'Poznámka'

		userSideMenu:
			label: "User side menu (uživatelská sekce)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		error404:
			type: group
			label: "Nastavení textu erroru 404"
			items:
				title:
					label: Nadpis
					type: text
				content1:
					label: "Obsah 1"
					type: tinymce
				content2:
					label: "Obsah 2"
					type: tinymce

		# emptyResultsTips:
		# 	type: group
		# 	label: "Text pro prázdné vyhledávání (Naše tipy)"
		# 	items:
		# 		content:
		# 			type: tinymce
		# 			label: "Obsah"

		locked:
			type: group
			label: "Box pro zamčený článek"
			items:
				title:
					type: text
					label: "Nadpis"
				text:
					type: textarea
					label: "Text"
				benefits:
					type: tinymce
					label: "Výhody registrace"

		stores:
			type: list
			label: "Výpis poboček - dodatečné položky"
			items:
				title:
					type: text
					label: "Nadpis"
				content:
					type: tinymce
					label: "Obsah"
				btn:
					extends: @cf.definitions.linkChoose
				img:
					type: image	# has size
					label: "Obrázek (min. 560x420)"

		store_info:
			type: group
			label: "Informace o pobočce"
			items:
				address:
					type: textarea
					label: "Adresa"
				hours:
					type: text
					label: "Provozní doba"
				images:
					type: image
					multiple: true
					label: "Obrázky prodejny"
				message:
					type: group
					label: "Info lišta"
					items:
						emoji:
							label: "Emoji (https://getemoji.com/)"
							type: text
						text:
							type: tinymce
							label: "Text"
						# bg:
						# 	type: select
						# 	label: "Barva pozadí"
						# 	defaultValue: 'warning'
						# 	options: [
						# 		{ label: "Zelená", value: "ok" },
						# 		{ label: "Žlutá (defaultní)", value: "warning" },
						# 		{ label: "Oranžová", value: "warning2" },
						# 		{ label: "Červená", value: "error" },
						# 		{ label: "Fialová", value: "violet" }
				transport:
					type: group
					label: "Způsoby dopravy"
					items:
						metro:
							type: textarea
							label: "Metro"
						bus:
							type: textarea
							label: "Autobus"
						tram:
							type: textarea
							label: "Tramvaj"
						parking:
							type: textarea
							label: "Parkování"
				mapEmbed:
					type: textarea
					label: "Kód embedovaé mapy"
				# map:
				# 	type: group
				# 	label: "Mapa"
				# 	items:
				# 		lat:
				# 			type: text
				# 			label: "Zem. šířka"
				# 		lng:
				# 			type: text
				# 			label: "Zem. délka"

		person_cta_ebook:
			extends: @cf.definitions.person_cta
			label: "CTA pro stažení e-booku"

		person_cta:
			extends: @cf.definitions.person_cta
			label: "CTA s osobou"

		header_ebook:
			type: group
			label: "Nastavení záhlaví"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 750x563)"
				file:
					type: file
					label: "E-book"
				usp:
					extends: @cf.definitions.usp_list
					label: "USP"


		header_about:
			type: group
			label: "Nastavení záhlaví"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 2880x630)"
				links:
					type: group
					label: "Odkazy"
					items:
						title:
							type: text
							label: "Nadpis"
						links:
							type: list
							label: "Odkazy"
							items:
								link:
									extends: @cf.definitions.linkChoose

		emptyBasket:
			type: group
			label: "Nastavení textu prázdného košíku"
			items:
				header:
					type: group
					label: "Košík v hlavičce"
					items:
						content:
							type: tinymce
							label: "Text"
				page:
					type: group
					label: "Košík na stránce"
					items:
						title:
							type: text
							label: "Nadpis"
						content:
							type: tinymce
							label: "Text"
		emptyCompare:
			type: group
			label: "Nastavení textu prázdného porovnávače"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 750x563)"
				title:
					type: text
					label: "Nadpis"
				content:
					type: tinymce
					label: "Text"
				linkChoose:
					extends: @cf.definitions.linkChoose
				hideBtn:
					type: checkbox
					label: "Skryt tlačítko"

		dronzone_categories:
			type: list
			label: "Vybrané kategorie (hlavička a komponenta DronZóny na HP)"
			items:
				page:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		bnrImg:
			type: group
			label: "Banner (košík v hlavičce, prázdný košík, doprava a platba)"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 1920x823)"
				link:
					extends: @cf.definitions.linkChoose

		header_usp:
			type: group
			label: "USP v záhlaví"
			items:
				items:
					type: list
					label: "USP"
					items:
						text:
							type: text

		job_info:
			type: group
			label: "Informace o práci"
			items:
				location:
					type: text
					label: "Místo"
				type_of_position:
					type: text
					label: "Typ práce"



		# error500:
		# 	type: group
		# 	label: "Nastavení textu erroru 500"
		# 	items:
		# 		name:
		# 			label: Název
		# 			type: text
		# 		annotation:
		# 			label: Anotace
		# 			type: text
		# 		content:
		# 			label: Obsah
		# 			type: tinymce

		# loaltyTexts:
		# 	type: group
		# 	items:
		# 		loayltyHeader:
		# 			type: text
		# 			label: 'Nadpis'
		# 		loayltyLink:
		# 			type: suggest
		# 			label: 'Odkaz v nadpisu'
		# 			subType: tree
		# 			url: @cf.suggestUrls.searchMutationPage
		# 		loayltyLinkName:
		# 			type: text
		# 			label: 'Název odkazu v nadpisu'
		# 		loayltyDescription:
		# 			type: textarea
		# 			label: 'Obsah'

		# loyaltyClub:
		# 	label: 'Věrnostní klub DK'
		# 	type: group
		# 	items:
		# 		nonregistered:
		# 			extends: @cf.loaltyTexts
		# 			label: 'Nepřihlášení'
		# 		registered:
		# 			extends: @cf.loaltyTexts
		# 			label: 'Přihlášení'

		# loaytyClubBonuses:
		# 	label: 'Výhody DK klubu'
		# 	type: group
		# 	items:
		# 		title:
		# 			type: text
		# 			label: 'Nadpis'
		# 		bonuses:
		# 			type: tinymce

		socials:
			type: group
			label: "Odkazy na sociální sítě"
			items:
				facebook:
					type: text
					label: "Facebook"
				tiktok:
					type: text
					label: "Tiktok"
				# x:
				# 	type: text
				# 	label: "X"
				instagram:
					type: text
					label: "Instagram"
				youtube:
					type: text
					label: "Youtube"


		courseType:
			type: group
			label: "Nastavení typu kurzu"
			items:
				tooltip_cs:
					type: text
					label: "tooltip (%cs-mutation-code%)"
				color:
					type: select
					label: "Barva"
					defaultValue: "blue"
					options: [
						{ label: "Modrá", value: "blue-light" },
						{ label: "Fialová", value: "purple-light" },
						{ label: "Zelená", value: "green-light" },
						{ label: "Oranžová", value: "orange-light" },
					]

		productComplectation:
			type: group
			label: "Nastavení komplekta produktu"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 214x161)"


		base:
			type: group
			label: "Nastavení stránky"
			items:
				show_eu_bnr:
					type: checkbox
					label: "Zobrazit EU banner v patičce"
					defaultValue: true
				crossroadImage:
					type: image # has size
					label: "Obrázek nebo SVG (rozcestník min.160x160 a submenu min. 420x315)"
				crossroadAnnotation:
					type: textarea
					label: "Anotace pro výpis v rozcestníku"

		mainImage:
			type: group
			label: "Hlavní obrázek"
			items:
				image:
					type: image # TODO
					label: "Obrázek (min. TODO)"

		# story:
		# 	type: group
		# 	label: "Příběh"
		# 	items:
		# 		photos:
		# 			extends: @cf.definitions.photos # has size
		# 			label: "Fotky (min. výška 750px)"
		# 		content:
		# 			extends: @cf.definitions.content
		# 		person:
		# 			extends: @cf.definitions.person


		footer_menu:
			type: group
			label: "Footer menu"
			items:
				footer_menu_1:
					type: group
					label: "1. sloupec"
					order: 1
					items:
						title:
							type: text
							label: "Nadpis"
						list:
							extends: @cf.definitions.linkChoose
							type: list
							label: "Položky menu"
				footer_menu_2:
					type: group
					label: "2. sloupec"
					order: 2
					items:
						title:
							type: text
							label: "Nadpis"
						list:
							extends: @cf.definitions.linkChoose
							type: list
							label: "Položky menu"

				footer_menu_3:
					type: group
					label: "3. sloupec"
					order: 3
					items:
						title:
							type: text
							label: "Nadpis"
						list:
							extends: @cf.definitions.linkChoose
							type: list
							label: "Položky menu"
				# footer_bottom:
				# 	type: group
				# 	label: "Spodní menu"
				# 	items:
				# 		list:
				# 			extends: @cf.definitions.linkChoose
				# 			type: list
				# 			label: "Položky menu"

		# benefits_footer:
		# 	extends: @cf.definitions.benefits
		# 	label: "Benefity"

		# benefits:
		# 	type: group
		# 	label: "Benefity"
		# 	items:
		# 		content:
		# 			extends: @cf.definitions.content
		# 		items:
		# 			extends: @cf.definitions.benefits

		videos:
			type: list
			label: "Videa"
			items:
				video:
					type: text
					label: "Odkaz na Youtube / Vimeo"
				image:
					type: image # has size
					label: "Obrázek (min. 1215x911)"

		badge:
			type: group
			label: "Badge"
			items:
				text:
					type: text
					label: "Text"
				type:
					type: select
					label: "Barva"
					defaultValue: "yellow"
					options: [
						{ label: "Žlutá", value: "yellow" },
						{ label: "Přechod", value: "rainbow" },
						{ label: "Červená", value: "red" },
						{ label: "Bílá", value: "outline" },
					]

		priceBox:
			type: group
			label: "Vzhled boxu s cenou"
			items:
				type:
					type: select
					label: "Barva"
					defaultValue: "default"
					options: [
						{ label: "Defaultní", value: "default" },
						{ label: "Cenová bomba (barevné podbarvení)", value: "bomb" },
						{ label: "Drak friday (tmavé podbarvení)", value: "drak" },
					]
		productUsp:
			type: list
			label: "USP"
			items:
				uspId:
					type: suggest
					label: "USP"
					subType: usp
					url: @cf.suggestUrls.searchUsp
		lastMinute:
			type: group
			label: "Last minute"
			items:
				daysCount:
					type: text
					subType: number
					label: Počet dnů před začátkem
				capacityCount:
					type: text
					subType: number
					label: Počet míst
		promoPriceDefinition:
			type: group
			label: "Limit ceny pro pro určení slevy [%]"
			items:
				percentLimit:
					type: text
					subType: number
					label: "Limit ceny pro pro určení slevy [%]"

		directBuy:
			type: group
			label: "Podmínky pro přímé vložení do košíku"
			items:
				limitPrice:
					type: text
					subType: number
					label: "Limit ceny, do které je možné položku vložit přímo do košíku"

		schedule:
			type: list
			label: "Harmonogram a průběh (pouze pro kurzy)"
			items:
				name:
					type: text
					label: "Název"
				duration:
					type: text
					label: "Délka"
				type:
					type: select
					label: "Typ"
					options: [
						{ label: "Online", value: "online" },
						{ label: "Naživo", value: "inperson" },
					]
				content:
					type: tinymce
					label: "Obsah hodiny"

		buyin:
			type: group
			label: "Kroky"
			items:
				title:
					type: text
					label: "Nadpis"
				steps:
					type: list
					label: "Kroky"
					items:
						text:
							type: textarea
				question:
					type: group
					label: "Box s dotazem"
					items:
						title:
							type: text
							label: "Nadpis"
						content:
							type: tinymce
							label: "Obsah"

		service_steps:
			type: group
			label: "Kroky"
			items:
				title:
					type: text
					label: "Nadpis"
				steps:
					type: list
					label: "Kroky"
					items:
						text:
							type: textarea
				boxes:
					type: list
					label: "Šedé boxy pod kroky"
					items:
						title:
							type: text
							label: "Nadpis"
						annot:
							type: textarea
							label: "Popis"
						image:
							type: image # TODO
							label: "Obrázek (min.320x240)"
						btn:
							type: group
							hasContentToggle: true
							label: "Tlačítko"
							items:
								type:
									type: radio
									inline: true
									isContentToggle: true
									defaultValue: 'custom'
									options: [
										{ label: "Přihlášení (v modalu)", value: "login" },
										{ label: "Vlastní", value: "custom" },
									]
								custom:
									extends: @cf.definitions.linkChoose
									label: "Vlastní"

		steps:
			type: group
			label: "Kroky"
			items:
				title:
					type: text
					label: "Nadpis"
				steps:
					type: list
					label: "Kroky"
					items:
						text:
							type: textarea

		buyin_prices:
			type: group
			label: "Příklad výkupních cen"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Ceny"
					items:
						name:
							type: text
							label: "Název"
						specification:
							type: text
							label: "Specifikace"
						state:
							type: text
							label: "Stav"
						price:
							type: text
							label: "Cena"

		prices:
			type: group
			label: "Ceník"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Ceny"
					items:
						name:
							type: text
							label: "Název"
						price:
							type: text
							label: "Cena"

		cookies:
			label: "Cookies"
			type: group
			items:
				title:
					label: "Nadpis"
					type: text
					value: ""
				text:
					label: "Text"
					type: tinymce
					value: ""
				btnSetPreferences:
					label: "Tlačítko - nastavit preference"
					type: text
					value: ""
				btnReject:
					label: "Tlačítko - Odmítnout"
					type: text
					value: ""
				btnConsentAndContinuation:
					label: "Tlačítko - souhlas a pokračování"
					type: text
					value: ""
				consentsTitle:
					label: "Nadpis - nastavení preferencí"
					type: text
					value: ""
				necessarilyLink:
					label: "Nezbytné - link"
					type: text
					value: ""
				necessarilyText:
					label: "Nezbytné - text"
					type: tinymce
					value: ""
				preferenceslLink:
					label: "Předvolby - link"
					type: text
					value: ""
				preferencesText:
					label: "Předvolby - text"
					type: tinymce
					value: ""
				analyticsLink:
					label: "Analytika - link"
					type: text
					value: ""
				analyticsText:
					label: "Analytika - text"
					type: tinymce
					value: ""
				marketingLink:
					label: "Marketingové - link"
					type: text
					value: ""
				marketingText:
					label: "Marketingové - text"
					type: tinymce
					value: ""
				btnConfirmSelected:
					label: "Tlačítko - potvrdit vybrané"
					type: text
					value: ""
				btnAcceptEverything:
					label: "Tlačítko - přijmout vše"
					type: text
					value: ""

		registration_benefits:
			type: group
			label: "Výhody registrace"
			items:
				content:
					type: tinymce
					label: "Obsah"

		reg_finish_ok:
			type: group
			label: "Po dokončení registrace"
			items:
				message:
					label: "Oznámení po dokončení registrace"
					type: tinymce

		bonds:
			type: group
			label: "Nastavení komponenty Dluhopisy"
			items:
				min:
					type: text
					label: "Minimální částka (pouze číslo bez mezer)"
				max:
					type: text
					label: "Maximální částka (pouze číslo bez mezer)"
				value:
					type: text
					label: "Výchozí částka (pouze číslo bez mezer)"
				step:
					type: text
					label: "Krok (pouze číslo bez mezer)"

		contactPerson:
			type: group
			label: "Kontaktní osoba"
			items:
				name:
					type: text
					label: 'Jméno'
				image:
					type: image # has size
					label: 'Fotka (min. 220x220)'

		contacts:
			type: group
			label: "Kontaktní údaje"
			items:
				mail:
					type: text
					label: "E-mail"
				phone:
					type: text
					label: "Telefon"
				phone_hours:
					type: text
					label: "Telefon - kontaktní hodiny"
				store_image:
					type: image # has size
					label: "Fotografie prodejny (min. 1400x1400)"
				store_address:
					type: text
					label: "Adresa prodejny (patička)"
				store_address_full:
					type: textarea
					label: "Adresa prodejny (objednávka - osobní vyzvednutí)"

		benefits:
			type: group
			label: "Benefity (obsah nad patičkou)"
			items:
				items:
					type: list
					label: "Benefity"
					items:
						icon:
							type: image
							label: "SVG ikona"
						text:
							type: text
							label: "Název"

		services:
			type: group
			label: "Služby (obsah nad patičkou)"
			items:
				title:
					type: text
					label: "Nadpis"
				items_highlighted:
					type: list
					label: "Služby - highlightované"
					items:
						title:
							type: text
							label: "Nadpis"
						page:
							type: suggest
							label: "Stránka"
							subType: tree
							url: @cf.suggestUrls.searchMutationPage
						btnText:
							type: text
							label: "Text tlačítka"
						text:
							type: text
							label: "Popis"
						image:
							type: image # has size
							label: "Obrázek (min. 1125x1500)"

				items:
					type: list
					label: "Služby"
					items:
						title:
							type: text
							label: "Nadpis"
						page:
							type: suggest
							label: "Stránka"
							subType: tree
							url: @cf.suggestUrls.searchMutationPage
						text:
							type: text
							label: "Popis"
						image:
							type: image # has size
							label: "Obrázek (min. 280x210)"

		featured:
			type: group
			label: "Produkt měsíce"
			items:
				product:
					type: suggest
					label: "Produkt"
					url: @cf.suggestUrls.searchProduct
					subType: product
				btnText:
					type: text
					label: "Text tlačítka"

		landing_header:
			type: group
			label: "Nastavení záhlaví"
			items:
				slides:
					type: list
					label: "Slidy"
					items:
						image:
							type: image # has size
							label: "Obrázek (min. 2880x630)"
						title1:
							type: text
							label: "Nadpis - 1. řádek"
						title2:
							type: text
							label: "Nadpis - 2. řádek (outline)"
						annot:
							type: textarea
							label: "Anotace"
						btns:
							type: list
							label: "Tlačítka"
							items:
								btn:
									extends: @cf.definitions.linkChoose
				categories:
					type: list
					label: "Kategorie"
					items:
						link:
							extends: @cf.definitions.linkChoose
						image:
							type: image # has size
							label: "Obrázek (nepovinné, přebírá se ze stránky, min. 280x210)"
						tag:
							type: text
							label: "Štítek"

		popular_course:
			type: group
			label: "Nejpopulárnější kurz"
			items:
				course:
					type: group
					label: "Kurz"
					items:
						title:
							type: text
							label: "Titulek"
						image:
							type: image # has size
							label: "Obrázek (min. 1028x770)"
						course:
							type: suggest
							label: "Kurz"
							url: @cf.suggestUrls.searchCourse
							subType: product
						annotation:
							label: "Anotace"
							type: tinymce
							tiny-type: lite
				video:
					type: group
					label: "Video"
					items:
						link:
							type: text
							label: "Odkaz na Youtube / Vimeo"
						poster:
							type: image # has size
							label: "Obrázek (min. 1028x770)"
				quote:
					type: textarea
					label: "Citace"

		usp:
			label: "USP"
			extends: @cf.definitions.usp

		types:
			type: group
			label: "Typy řidičáků a kurzů"
			items:
				licence:
					type: group
					label: "Typy řidičáků"
					items:
						title:
							type: text
							label: "Nadpis"
						desc:
							type: textarea
							label: "Popis"
						items:
							type: list
							label: "Položky"
							items:
								link:
									extends: @cf.definitions.linkChoose
								desc:
									type: textarea
									label: "Popis"
				course:
					type: group
					label: "Typy kurzů"
					items:
						title:
							type: text
							label: "Nadpis"
						desc:
							type: textarea
							label: "Popis"
						items:
							type: list
							label: "Položky"
							items:
								link:
									extends: @cf.definitions.linkChoose
								desc:
									type: textarea
									label: "Popis"

		references:
			extends: @cf.definitions.references
			label: "Reference"

		didyouknow:
			type: group
			label: "Víte že..."
			items:
				img:
					type: image # has size
					label: "Obrázek (min. 340x276)"
				text:
					label: "Nadpis (nepovinné)"
					type: text
				page:
					label: "Článek"
					type: suggest
					subType: blogLocalization
					url: @cf.suggestUrls.searchMutationBlogs

		fleet:
			type: group
			label: "Flotila"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Anotace"
				items:
					type: list
					label: "Produkty"
					items:
						link:
							type: suggest
							label: "Produkt"
							url: @cf.suggestUrls.searchProduct
							subType: product
						flag:
							extends: @cf.definitions.flag
						tooltip:
							type: textarea
							label: "Obsah tooltipu"
		lector:
			type: group
			label: "Lektor"
			items:
				image:
					type: image # has size
					label: "Fotografie (min. 560x560)"
				video:
					type: text
					label: "Odkaz na Youtube / Vimeo"
				position:
					type: text
					label: "Pozice"
				text:
					type: textarea
					label: "Text"

		position_cta:
			type: group
			label: "CTA na konci detailu pozice"
			items:
				title1:
					type: text
					label: "Nadpis - levý sloupec"
				content1:
					type: tinymce
					label: "Obsah - levý sloupec"
				title2:
					type: text
					label: "Nadpis - pravý sloupec"
				content2:
					type: tinymce
					label: "Obsah - pravý sloupec"


		lecturers:
			type: group
			label: "Lektoři"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Anotace"
				items:
					type: list
					label: "Lektoři"
					items:
						image:
							type: image # has size
							label: "Fotografie (min.438x750)"
						video:
							type: text
							label: "Odkaz na Youtube / Vimeo"
						name:
							type: text
							label: "Jméno a příjmení"
						text:
							type: textarea
							label: "Text"

		claimsInfo:
			type: group
			label: "Reklamace - text v šedém boxu"
			items:
				content:
					type: tinymce

		mentions:
			type: group
			label: "Napsali a natočili o nás"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Anotace"
				logos:
					type: image # has size
					label: "Loga (min. 240x135)"
					multiple: true
				# items:
				# 	type: list
				# 	label: "Položky"
				# 	items:
				# 		page:
				# 			type: suggest


		categories:
			extends: @cf.definitions.categories
			label: "Kategorie"

		where:
			type: group
			label: "Kde školíme"
			items:
				title:
					type: text
					label: "Nadpis"
				text:
					type: textarea
					label: "Text"
				locationText:
					type: textarea
					label: "Text vedle ikony lokace"
				images:
					type: image # has size
					multiple: true
					label: "Obrázky (max. 5x, min. 1200x1200)"

		serviceForm:
			type: group
			label: "Obsahvé texty pro servisní formulář"
			items:
				photos:
					type: tinymce
					label: "Jaké fotky nahrávat"
				diagnostics:
					type: tinymce
					label: "Diagnostika"
				expressService:
					type: tinymce
					label: "Expresní servis"
				accessories:
					type: tinymce
					label: "Příslušenství"

		faq:
			extends: @cf.definitions.faq
			label: "FAQ"

		join:
			type: group
			label: "Proč se chceš přidat k nám"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Text"
				usp:
					type: list
					label: "USP"
					items:
						text:
							type: text

		presskit:
			type: list
			label: "Presskit"
			items:
				file:
					type: file
					label: "Soubor"
		virtual_category:
			extends: @cf.definitions.linkChoose
			label: "Virtuání kategorie"


		superkategories:
			type: list
			label: "Superkategorie"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 1920x823)"
				link:
					extends: @cf.definitions.linkChoose
					label: "Stránka / odkaz"

		supercategory_groups:
			type: list
			label: "Superkategorie group"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					extends: @cf.fields.superkategories

		paidByLo:
			type: group
			label: "Nastavení stránky"
			items:
				video:
					type: group
					label: "Video na děkovné stránce 100% úhrada"
					items:
						link:
							type: text
							label: "Odkaz na Youtube / Vimeo"
						image:
							type: image # has size
							label: "Zástupný obrázek (min. 720x405)"
				video82:
					type: group
					label: "Video na děkovné stránce 82% úhrada"
					items:
						link:
							type: text
							label: "Odkaz na Youtube / Vimeo"
						image:
							type: image # has size
							label: "Zástupný obrázek (min. 720x405)"
				upUrl:
					type: text
					label: "Odkaz na stránku UP 100% úhrada"
		paidByLo82:
			type: group
			label: "Nastavení UP 82% úhrada"
			items:
				upUrl:
					type: text
					label: "Odkaz na stránku UP 82% úhrada"

		manualsToDownload:
			type: list
			label: "Manualy ke stažení"
			items:
				file:
					type: file
					label: "Soubor"
				title:
					type: text
					label: "Nadpis"


		faqCategoryBanner:
			type: group
			label: "Banner"
			items:
				image:
					type: image # TODO
					label: "Obrázek / SVG"
				link:
					extends: @cf.definitions.linkChoose
					label: "Stránka / odkaz"
				textDecor:
					type: text
					label: "Text 1"
					defaultValue: "Víte že..."
				text:
					type: textarea
					label: "Text 2"

		specialistReview:
			type: group
			label: "Recenze speciality"
			items:
				text:
					type: textarea
					label: "Text"
				image:
					type: image
					label: "Obrázek (min. 750x750)"
				author:
					type: suggest
					label: "Autor"
					url: @cf.suggestUrls.searchAuthors
					subType: author
				blog:
					type: suggest
					label: "Článek"
					url: @cf.suggestUrls.searchBlog
					subType: blog
				advantages:
					type: list
					label: "Výhody"
					items:
						text:
							type: text
				disadvantages:
					type: list
					label: "Nevýhody"
					items:
						text:
							type: text





		catalogBanner:
			type: group
			label: "Bannery"
			items:
				banners:
					extends: @cf.definitions.bannerChoose

		packageContents:
			type: list
			label: "Obsah balení"
			items:
				parameterValue:
					type: suggest
					label: "Hodnota parametru"
					url: @cf.suggestUrls.searchParameterValuesPackageContents
					subType: parameterValue
				amount:
					type: text
					subType: number
					label: "Počet ks"



		clientTestimonial:
			type: list
			label: "Co o nás říkají klienti"
			items:
				clientTestimonial:
					type: suggest
					label: "Článek"
					url: @cf.suggestUrls.searchClientTestimonial
					subType: clientTestimonial

		blogMostReadArticlesDays:
			type: group
			label: "Dny pro zobrazení článků"
			items:
				blogMostReadArticlesDays:
					type: text
					subType: number
					defaultValue: 7


		compare:
			type: group
			label: "Nastavení porovnávače"
			items:
				shouldReverseCategoryPathForCompare:
					type: checkbox
					label: "Obrátit cestu kategorie"
					defaultValue: false

		parameterCustomFields:
			type: group
			label: "Vlastní pole"
			items:
				isMainForProductDetail:
					type: checkbox
					label: "Highlightovaný parametr"
					defaultValue: false
				icon:
					type: text
					label: "Ikona (emoji)"
				description_cs:
					type: textarea
					label: "Popis (CS)"
				description_sk:
					type: textarea
					label: "Popis (SK)"
				link:
					extends: @cf.definitions.linkChoose
					label: "Odkaz na stránku"


	suggestUrls:
		searchMutationPage:
			searchParameterName: search
			link: "/superadmin/search2/page-in-mutation"
			params: []
		searchBlogTag:
			searchParameterName: search
			link: "/superadmin/search2/blog-tag"
			params: []
		searchPage:
			searchParameterName: search
			link: "/superadmin/search2/page"
			params: []
		searchProduct:
			searchParameterName: search
			link: "/superadmin/search2/product"
			params: []
		searchCourse:
			searchParameterName: search
			link: "/superadmin/search2/product"
			params: ['isCourse': 1]
		searchParameter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: []
		searchParameterForDetail:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: [ 'onlyForDetail': 1]
		searchParameterForFilter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: [ 'types': ['select', 'multiselect', 'number'], 'onlyForFilter': 1]
		searchParameterValuesPackageContents:
			searchParameterName: search
			link: "/superadmin/search2/parameter-values-search"
			params: [ uid: 'productComplectation']
		searchSeoLinkParameterValues:
			searchParameterName: search
			link: "/superadmin/search2/seolink-parameter-values"
			params: []
		searchAuthors: <AUTHORS>
			link: "/superadmin/search2/author"
			params: []
		searchMutationBlogs:
			searchParameterName: search
			link: "/superadmin/search2/blog-in-mutation"
			params: []
		searchBlog:
			searchParameterName: search
			link: "/superadmin/search2/blog"
			params: []
		searchMutationAlias:
			searchParameterName: search
			link: "/superadmin/search2/alias"
			params: []
		searchState:
			searchParameterName: search
			link: "/superadmin/search2/state"
			params: []
		searchPaymentMethod:
			searchParameterName: search
			link: "/superadmin/search2/payment-method"
			params: []
		searchTags:
			searchParameterName: search
			link: "/superadmin/search2/tags"
			params: []
		searchTagLocalizations:
			searchParameterName: search
			link: "/superadmin/search2/tag-localizations"
			params: []
		searchUsp:
			searchParameterName: search
			link: "/superadmin/search2/usps"
			params: []
		searchGlossaryTag:
			searchParameterName: search
			link: "/superadmin/search2/glossary-tag"
			params: []
		searchFaqCategory:
			searchParameterName: search
			link: "/superadmin/search2/page-in-mutation"
			params: ['templates': ':FAQ:Front:FAQ:default']
		searchBanner:
			searchParameterName: search
			link: "/superadmin/search2/banner-localizations"
			params: []
		searchClientTestimonial:
			searchParameterName: search
			link: "/superadmin/search2/client-testimonial"
			params: []
		searchClientTestimonialLocalization:
			searchParameterName: search
			link: "/superadmin/search2/client-testimonial-localization"
			params: []

	templates:
		#template: [customfields]

		# examples - todo to WIKI:
		# WIKI - https://www.notion.so/superkoders/Vlastn-pole-Custom-Fields-verze-1-0-2c3322c358224c769c0bdb1a9593b6d2
		#1) :Front:Page:default: [customfields] # CF pro stránku s šablonou Page:default
		#2) Product:detail: [customfields] # CF pro produkt
		#3) product-AAA: [customfields] # CF pro produkt jehož nejvyšším rodičem (hlavní katgorie) je UID produktové kategorie AAA
		#4) uid-XXX: [customfields] # CF pro stránku s uid = XXX
		#5) parameter-YYY: [customfields] #CF pro parametr s uid = YYY
		#6) parameters: [customfields] #CF pro všechny parametry obecně, parameter-YYY přebíjí
		#7) banner-ZZZ: [customfields] #CF pro banner s pozici = ZZZ
		#8) CF pro všechny stránky ve stromě, nasatvuje se do speciální sekce: customFieldsTemplates
		#9) user-ROLE || users


		mutation: [@cf.mutationData, @cf.serviceMenu, @cf.footer_menu, @cf.benefits, @cf.services, @cf.error404, @cf.bnrImg]
		uid-title: [@cf.base, @cf.landing_header, @cf.featured, @cf.usp, @cf.didyouknow]
		uid-eshop: [@cf.supercategory_groups]

		# User
		uid-userSection: [@cf.userSideMenu, @cf.userMenuLoggedUser]
		uid-userLogin: [@cf.registration_benefits]
		:Front:User:registrationOk: [@cf.reg_finish_ok]
		:Front:User:default: [@cf.base]
		uid-userClaims: [@cf.claimsInfo]

		# Pages
		:Front:Page:default: [@cf.base, @cf.header, @cf.header_usp]
		:Front:Page:photobank: [@cf.base, @cf.header]
		:Front:Page:references: [@cf.base, @cf.header]
		:Front:Page:career: [@cf.base, @cf.header, @cf.header_usp, @cf.join, @cf.position_cta]
		:Front:Page:careerDetail: [@cf.base, @cf.job_info]
		:Front:Page:investment: [@cf.base, @cf.header, @cf.bonds]
		:Front:Page:sustainable: [@cf.base, @cf.header]
		:Front:Page:compare: [@cf.base]
		:Front:Page:deliveryAndPayment: [@cf.base, @cf.header]
		:Front:Page:ebooks: [@cf.base, @cf.header, @cf.header_usp]
		:Front:Page:ebook: [@cf.base, @cf.header_ebook, @cf.person_cta_ebook]
		:Front:Page:store: [@cf.base, @cf.header, @cf.store_info]
		:Front:Page:about: [@cf.base, @cf.header_about]
		:Front:Page:media: [@cf.base, @cf.header, @cf.contactPerson, @cf.presskit, @cf.mentions]
		# :Front:FAQ:category: [@cf.base, @cf.header, @cf.faq]
		# :Front:Page:faqDetail: [@cf.base, @cf.header]
		:Front:Page:paidCourses: [@cf.base, @cf.header]
		:Front:Page:paidCoursesQuestionare: [@cf.base, @cf.header]
		:Front:Page:serviceForm: [@cf.base, @cf.header, @cf.serviceForm]
		:Front:Page:filmingShooting: [@cf.base, @cf.header, @cf.person_cta, @cf.header_usp]
		:Front:Page:insurance: [@cf.base, @cf.header]
		:Front:Page:terms: [@cf.base, @cf.header]
		:Front:Page:termDetail: [@cf.base]
		:Front:Page:services: [@cf.base, @cf.header, @cf.categories]
		:Front:Page:service: [@cf.base, @cf.header, @cf.header_usp, @cf.service_steps, @cf.prices, @cf.person_cta]
		:Front:Page:rental: [@cf.base, @cf.header, @cf.header_usp, @cf.steps, @cf.prices, @cf.person_cta]
		:Front:Page:rentalStep1: [@cf.base]
		:Front:Page:rentalStep2: [@cf.base]
		:Front:Page:rentalStep3: [@cf.base]
		:Front:Page:buyin: [@cf.base, @cf.header, @cf.header_usp, @cf.buyin, @cf.buyin_prices, @cf.person_cta]
		:Front:Page:academy: [@cf.landing_header, @cf.base, @cf.popular_course, @cf.types, @cf.references, @cf.fleet, @cf.lecturers, @cf.where, @cf.faq]
		:Front:Page:contact: [@cf.base, @cf.header, @cf.contactPerson, @cf.contacts, @cf.socials, @cf.stores]
		:Front:PaidByLo:default: [@cf.paidByLo]
		:Front:Compare:default: [@cf.emptyCompare]

		# Blog
		:Blog:Front:Blog:default: [@cf.base, @cf.dronzone_categories, @cf.locked, @cf.blogMostReadArticlesDays]

		# FAQ
		:Front:Faq:default: [@cf.base, @cf.header]
		:Faq:Front:Faq:default: [@cf.faqCategoryBanner, @cf.clientTestimonial]
		:Faq:Front:Faq:detail: [@cf.base, @cf.header]

		# Order
		uid-cart: [@cf.emptyBasket]
		uid-deliveryMethod: [@cf.deliveryPayment]
		uid-paymentMethod: [@cf.deliveryPayment, @cf.paymentAccount]

		# Catalog & product
		:Front:Catalog:default: [@cf.base, @cf.virtual_category, @cf.lastMinute, @cf.promoPriceDefinition, @cf.directBuy, @cf.catalogBanner, @cf.compare]
		:Front:Product:detail: [@cf.manualsToDownload, @cf.faq, @cf.specialistReview, @cf.packageContents]
		product-class: [@cf.videos, @cf.badge, @cf.productUsp, @cf.priceBox, @cf.paidByLo82, @cf.faq]
		parameterValue-prubehKurzu: [@cf.courseType]
		parameterValue-color: [@cf.parameterInPicker]
		parameterValue-productComplectation: [@cf.productComplectation]


		# Other
		user-lector: [@cf.lector]

		#:Front:Class:default: [@cf.base]
		# uid-loyaltyProgram: [@cf.loaytyClubBonuses]
		# uid-search: [@cf.emptyResultsTips]
		# :Front:Product:detail: [@cf.videos, @cf.badge, @cf.schedule, @cf.priceBox] # @cf.product, @cf.productImagePages
		# parameterValue: [@cf.image]
		# uid-cookie: [@cf.cookies]
		# users: [@cf.pickupPoints]

		parameter: [@cf.parameterCustomFields]
