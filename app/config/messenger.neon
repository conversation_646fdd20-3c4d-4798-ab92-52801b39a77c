extensions:
	messenger: Contributte\Messenger\DI\MessengerExtension

monolog:
	channel:
		messenger:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/SymfonyMessenger/default.log, 30, Monolog\Level::Info)
				#- Monolog\Handler\NullHandler
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
		messenger_failed:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/SymfonyMessenger/failed.log, 30, Monolog\Level::Error)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

messenger:
	failureTransport: failure
	debug:
		panel: %debugMode%

	bus:
		elasticBus:
	transportFactory:
		dbal: @App\Model\Messenger\Transport\DbalTransportFactory
	transport:
		elasticFront:
			dsn: @messenger.dsn::amqp('messages')
			#dsn: @messenger.dsn::sync()
		elasticPriorityFront:
			dsn: @messenger.dsn::amqp('priority_messages')
			#dsn: @messenger.dsn::sync()
		clonerFront:
			#dsn: @messenger.dsn::redis('cloner')
			dsn: @messenger.dsn::sync()
		failure:
			dsn: @messenger.dsn::dbal('failure') #@messenger.dsn::amqp('fail')
			options:
				table: failed_messages
		cronCommands:
			dsn: @messenger.dsn::amqp('cron_commands')
			#dsn: @messenger.dsn::sync()
		defaultFront:
			dsn: @messenger.dsn::amqp('default')
			#retryStrategy:
			#	maxRetries: 5
			#	delay: 1000
			#	multiplier: 2
			#	maxDelay: 0 # 45 min
			#dsn: @messenger.dsn::sync()
		warmUpFront:
			#dsn: @messenger.dsn::amqp('warm_up')
			dsn: @messenger.dsn::sync()
		erpFront:
			dsn: @messenger.dsn::amqp('erp')
			#dsn: @messenger.dsn::sync()
	logger:
		httpLogger: @monolog.logger.messenger
		consoleLogger: @monolog.logger.messenger

	routing:
		App\Model\Messenger\Elasticsearch\Product\Message\QuickReplaceProductMessage: [elasticPriorityFront]
		App\Model\Messenger\Elasticsearch\Product\Message\ReplaceProductMessage: [elasticFront]

		App\Model\Messenger\Elasticsearch\Product\Message\DeleteProductMessage: [elasticFront]
		App\Model\Messenger\Elasticsearch\Product\Message\CreateProductMessage: [elasticFront]

		App\Model\Messenger\Elasticsearch\All\Message\ReplaceAllMessage: [elasticFront]
		App\Model\Messenger\Elasticsearch\All\Message\DeleteAllMessage: [elasticFront]

		App\Model\Messenger\Elasticsearch\Common\Message\ReplaceCommonMessage: [elasticFront]
		App\Model\Messenger\Elasticsearch\Common\Message\DeleteCommonMessage: [elasticFront]

		App\Model\Messenger\Cloner\CloneMessage: [clonerFront]


		App\PostType\Tag\Model\Checker\Task\TagMessage: [elasticFront]

		App\Model\Messenger\Default\HeurekaOvereno\HeurekaOverenoMessage: [defaultFront]
		App\Model\Messenger\WarmUp\ProductBox\FillProductBoxMessage: [warmUpFront]

		App\Model\Messenger\Erp\Change\ChangeMessage: [erpFront]
		App\Model\Messenger\Erp\Bulk\BulkMessage: [erpFront]
		App\Model\Messenger\Erp\Stock\StockMessage: [defaultFront]
		App\Model\Messenger\Erp\Country\CountryMessage: [defaultFront]
		App\Model\Messenger\Erp\Partner\PartnerMessage: [defaultFront]
		App\Model\Messenger\Erp\Product\ProductMessage: [defaultFront]
		App\Model\Messenger\Erp\ProductUsp\ProductUspMessage: [defaultFront]
		App\Model\Messenger\Erp\ProductCourse\ProductCourseMessage: [defaultFront]
		App\Model\Messenger\Erp\Price\PriceMessage: [defaultFront]
		App\Model\Messenger\Erp\ProductTag\ProductTagMessage: [defaultFront]
		App\Model\Messenger\Erp\ProductCategory\ProductCategoryMessage: [defaultFront]

		App\Model\Messenger\Erp\Sync\SyncMessage: [defaultFront]

		App\Model\Messenger\Default\PageProductParser\PageProductParserMessage: [defaultFront]


services:
	messenger.dsn: App\Model\Messenger\MessengerDsn(%redis%, %amqp%)
	- App\Model\Messenger\Elasticsearch\ElasticBusWrapper(@messenger.bus.elasticBus.bus)
	- App\Model\Messenger\Elasticsearch\ConsumerHelper
	# Default consumers
	- App\Model\Messenger\Default\HeurekaOvereno\HeurekaOverenoConsumer
	- App\Model\Messenger\Default\PageProductParser\PageProductParserConsumer

	# ES consumers
	- App\Model\Messenger\Elasticsearch\Product\Consumer\ReplaceProductConsumer
	- App\Model\Messenger\Elasticsearch\Product\Consumer\DeleteProductConsumer
	- App\Model\Messenger\Elasticsearch\Product\Consumer\CreateProductConsumer
	- App\Model\Messenger\Elasticsearch\Product\Consumer\QuickReplaceProductConsumer

	- App\Model\Messenger\Elasticsearch\All\Consumer\ReplaceAllConsumer
	- App\Model\Messenger\Elasticsearch\All\Consumer\DeleteAllConsumer

	- App\Model\Messenger\Elasticsearch\Common\Consumer\ReplaceCommonConsumer
	- App\Model\Messenger\Elasticsearch\Common\Consumer\DeleteCommonConsumer

	- App\Model\Messenger\Cloner\CloneConsumer

	- App\Model\Messenger\WarmUp\ProductBox\FillProductBoxConsumer

	- App\Model\Messenger\Erp\Change\ChangeConsumer
	- App\Model\Messenger\Erp\Bulk\BulkConsumer

	- App\Model\Messenger\Erp\Stock\StockConsumer
	- App\Model\Messenger\Erp\Partner\PartnerConsumer
	- App\Model\Messenger\Erp\Product\ProductConsumer
	- App\Model\Messenger\Erp\Price\PriceConsumer
	- App\Model\Messenger\Erp\Sync\SyncConsumer
	- App\Model\Messenger\Erp\ProductCourse\ProductCourseConsumer
	- App\Model\Messenger\Erp\ProductUsp\ProductUspConsumer
	- App\Model\Messenger\Erp\Country\CountryConsumer
	- App\Model\Messenger\Erp\ProductTag\ProductTagConsumer
	- App\Model\Messenger\Erp\ProductCategory\ProductCategoryConsumer

	- App\Model\Messenger\FailedEventSubscriber

	- App\PostType\Tag\Model\Checker\Task\TagConsumer

	- App\Model\Messenger\Transport\DbalTransportFactory


	- Symfony\Component\Messenger\EventListener\AddErrorDetailsStampListener
	events.dispatcher: # chybi v knihovne contributte/messenger - EventPass.php
		setup:
			- addSubscriber(@Symfony\Component\Messenger\EventListener\AddErrorDetailsStampListener)
