<?php

declare(strict_types=1);

namespace App\Model\ShoppingCart;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;

final class ShoppingCartAccessor {

	private ?ShoppingCartInterface $shoppingCart = null;

	public function __construct(
		private readonly ShoppingCartFactory $shoppingCartFactory,
		private readonly EventDispatcherInterface $eventDispatcher,
		private readonly bool $consoleMode
	)
	{
	}

	public function get(): ShoppingCartInterface
	{
		if ($this->shoppingCart === null) {
			if ($this->consoleMode) {
				$this->shoppingCart = new FakeShoppingCart();
			} else {
				$this->shoppingCart = $this->shoppingCartFactory->create();
				$this->eventDispatcher->addSubscriber($this->shoppingCart);
			}
		}

		return $this->shoppingCart;
	}
}
