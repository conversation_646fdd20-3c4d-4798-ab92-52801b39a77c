<?php declare(strict_types = 1);

namespace App\Model\ShoppingCart\Handlers;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Orm;
use App\Model\Security\User;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\ShoppingCart\Storage\Storage;

final readonly class DefaultUserAuthenticationHandler implements UserAuthenticationHandler
{

	public function __construct(
		private User $userContext,
		private Orm $orm,
		private OrderModel $orderModel,
	)
	{
	}
	public function handleLoggedIn(ShoppingCartInterface $shoppingCart, Storage $storage, ?Order $order): void
	{
		bdump('logged in');
		$shoppingCart->refresh();
		/*$userEntity = $this->orm->user->getById($this->userContext->getId());

		if ($order !== null && $order->user === null) {
			// remove previous draft orders
			$prevOrders = $this->orm->order->findBy(['state' => OrderState::Draft, 'user' => $userEntity]);
			foreach ($prevOrders as $prevOrder) {
				$this->orderModel->remove($prevOrder);
			}

			$this->orm->flush();

			// set unlogged order user entity
			$order->user = $userEntity;
			$this->orm->persistAndFlush($order);
		}*/
	}

	public function handleLoggedOut(ShoppingCartInterface $shoppingCart, Storage $storage, ?Order $order): void
	{
		bdump('logged out');
		bdump($order);
		bdump($shoppingCart);
		$shoppingCart->refresh();
		//$storage->remove();
	}
}
