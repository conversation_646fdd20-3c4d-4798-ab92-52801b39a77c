<?php declare(strict_types = 1);

namespace App\Model\ShoppingCart\Handlers;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Orm;
use App\Model\Security\User;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\ShoppingCart\Storage\Storage;

final readonly class DefaultUserAuthenticationHandler implements UserAuthenticationHandler
{

	public function __construct(
		private User $userContext,
		private Orm $orm,
		private OrderModel $orderModel,
	)
	{
	}
	public function handleLoggedIn(ShoppingCartInterface $shoppingCart, Storage $storage, ?Order $order): void
	{
		$shoppingCart->refresh();
	}

	public function handleLoggedOut(ShoppingCartInterface $shoppingCart, Storage $storage, ?Order $order): void
	{
		$shoppingCart->refresh();
	}
}
