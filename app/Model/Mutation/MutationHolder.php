<?php declare(strict_types = 1);

namespace App\Model\Mutation;

use App\Model\Mutation\Exceptions\MutationIsNotAttachedException;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Mutation\Exceptions\MutationNotSetException;

final class MutationHolder
{

	private ?Mutation $mutation = null;

	public function __construct()
	{
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getMutation(): Mutation
	{
		if (!isset($this->mutation)) {
			throw new MutationNotSetException('Mutation not set. You must call setMutation() first!');
		}

		return $this->mutation->isAttached() ? $this->mutation : throw new MutationIsNotAttachedException('Mutation is not attached. You probably called Orm::clear(). Use OrmCleaner::safeClear() instead.');
	}

}
