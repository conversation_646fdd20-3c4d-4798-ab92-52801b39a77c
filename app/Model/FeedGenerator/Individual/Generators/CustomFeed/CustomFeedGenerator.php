<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Generators\CustomFeed;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Generators\Generator;
use App\Model\FeedGenerator\Core\Generators\GeneratorInterface;
use App\Model\FeedGenerator\Individual\DTO\Mergado\ClassEvent;
use App\Model\FeedGenerator\Individual\DTO\Mergado\OnlineCourseWithoutEvent;
use App\Model\FeedGenerator\Individual\DTO\Mergado\Variant;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Setup;
use App\Model\Time\CurrentDateTimeProvider;
use App\Model\TranslatorDB;
use Contributte\Monolog\LoggerManager;
use XMLWriter;

final class CustomFeedGenerator extends Generator implements GeneratorInterface
{

	public function __construct(
		protected readonly XMLWriter $XMLWriter,
		protected readonly LoggerManager $loggerManager,
		protected readonly Orm $orm,
		protected readonly MutationHolder $mutationHolder,
		protected readonly TranslatorDB $translatorDB,
		protected readonly CurrentDateTimeProvider $currentDateTimeProvider,
		protected readonly Repository $productRepository
	)
	{
		$this->addLogger('xmlGenerator');
	}

	protected function createRootElementName(): void
	{
		$this->setRootElementName('CHANNEL');
	}

	protected function createElementName(): void
	{
		$this->setElementName('ITEM');
	}

	public function generate(): GeneratorInterface
	{
		$this->logger?->info(self::class . ' / Starting generating feed');

		$this->configureWriter();
		$this->createRootElementName();
		$this->createElementName();

		$this->getWriter()->startElement($this->getRootElementName());
		foreach ($this->getRootElementAttributes() as $name => $value) {
			$this->getWriter()->writeAttribute($name, $value);
		}

		foreach ($this->getElements() as $elementName => $elementValue) {
			$this->getWriter()->writeElement($elementName, $elementValue);
		}

		$this->dtosCount = 0;
		$batchCount = 0;

		while (($dtoObjects = $this->getItems(200, $batchCount)) !== null) {
			$dtosBatchCount = 0;
			foreach ($dtoObjects as $dto) {
				$this->createElement($dto);
				$dtosBatchCount++;
				$this->dtosCount++;
				unset($dto);
			}

			$this->logger?->info(self::class . ' / Batch of {count} products completed.', ['batchCount' => $dtosBatchCount, 'totalCount' => $this->dtosCount]);

			$this->orm->clear();
			$batchCount++;
		}

		$this->getWriter()->endElement();

		$this->logger?->info(self::class . ' / Finished generating feed');
		return $this;
	}

	/**
	 * @param Variant|ClassEvent|OnlineCourseWithoutEvent $dto
	 */
	protected function createElement(DTOInterface $dto): void
	{
		$this->getWriter()->startElement($this->getElementName());

		$this->createAttachedProducts($dto);
		$this->writeElement('ITEM_ID', $dto->getItemId());
		$this->writeElement('ACCESSORY', $dto->getAccessory());
		$this->writeElement('ADULT', $dto->getAdult());
		$this->writeElement('AGE_GROUP', $dto->getAgeGroup());
		$this->writeElement('AVAILABILITY', $dto->getAvailability());
		$this->writeElement('BENEFIT', $dto->getBenefit());
		$this->writeElement('BRAND', $dto->getBrand());
		$this->writeElement('BRAND_URL', $dto->getBrandUrl());
		$this->writeElement('CATEGORY', $dto->getCategory());
		$this->writeElement('CATEGORY_DESCRIPTION', $dto->getCategoryDescription());
		$this->writeElement('CATEGORY_ID', $dto->getCategoryId());
		$this->writeElement('CATEGORY_MAX_PRICE_VAT', $dto->getCategoryMaxPriceVat());
		$this->writeElement('CATEGORY_MIN_PRICE_VAT', $dto->getCategoryMinPriceVat());
		$this->writeElement('CATEGORY_NAME', $dto->getCategoryName());
		$this->writeElement('CATEGORY_QUANTITY', $dto->getCategoryQuantity());
		$this->writeElement('CATEGORY_URL', $dto->getCategoryUrl());
		$this->writeElement('CPC', $dto->getCpc());
		$this->writeElement('CPC_FULLTEXT', $dto->getCpcFulltext());
		$this->writeElement('COLOR', $dto->getColor());
		$this->writeElement('CONDITION', $dto->getCondition());
		$this->writeElement('CONDITION_DESCRIPTION', $dto->getConditionDescription());
		$this->writeElement('COST', $dto->getCost());
		$this->writeElement('COST_VAT', $dto->getCostVat());
		$this->writeElement('CURRENCY', $dto->getCurrency());
		$this->writeElement('CUSTOM_LABEL', $dto->getCustomLabel());
		$this->writeElement('DELIVERY_DAYS', $dto->getDeliveryDays());
		$this->createElementDelivery($dto);
		$this->createDepot($dto);
		$this->writeElement('DESCRIPTION', $dto->getDescription());
		$this->writeElement('DESCRIPTION_SHORT', $dto->getDescriptionShort());
		$this->writeElement('DUES', $dto->getDues());
		$this->writeElement('EAN', $dto->getEan());
		$this->writeElement('ENERGY_CLASS', $dto->getEnergyClass());
		$this->writeElement('EXTRA_MESSAGE', $dto->getExtraMessage());
		$this->writeElement('GENDER', $dto->getGender());
		$this->writeElement('HIGHLIGHT', $dto->getHighlight());
		$this->writeElement('IDENTIFIER_EXISTS', $dto->getIdentifierExists());
		$this->writeElement('ISBN', $dto->getIsbn());
		$this->writeElement('IMAGE', $dto->getImage());
		$this->createImageAlternative($dto);
		$this->writeElement('ITEMGROUP_ID', $dto->getItemGroupId());
		$this->writeElement('MATERIAL', $dto->getMaterial());
		$this->writeElement('NAME_EXACT', $dto->getNameExact());
		$this->writeElement('NAME_COMMERCIAL', $dto->getNameCommercial());
		$this->createParams($dto);
		$this->writeElement('PRIORITY', $dto->getPriority());
		$this->writeElement('PRODUCER', $dto->getProducer());
		$this->writeElement('PRODUCTNO', $dto->getProductNo());
		$this->writeElement('PRODUCT_TYPE', $dto->getProductType());
		$this->writeElement('RELEASE_DATE', $dto->getReleaseDate());
		$this->writeElement('SIZE', $dto->getSize());
		$this->writeElement('URL', $dto->getUrl());
		$this->writeElement('URL_ADWORDS', $dto->getUrlAdwords());
		$this->writeElement('URL_MOBILE', $dto->getUrlMobile());
		$this->writeElement('PATTERN', $dto->getPattern());
		$this->writeElement('PRICE', $dto->getPrice());
		$this->writeElement('PRICE_VAT', $dto->getPriceVat());
		$this->writeElement('PRICE_RETAIL', $dto->getPriceRetail());
		$this->writeElement('PRICE_RETAIL_VAT', $dto->getPriceRetailVat());
		$this->writeElement('PRICE_DISCOUNT', $dto->getPriceDiscount());
		$this->writeElement('PRICE_DISCOUNT_VAT', $dto->getPriceDiscountVat());
		$this->writeElement('SHIPPING_LABEL', $dto->getShippingLabel());
		$this->writeElement('SHIPPING_SIZE', $dto->getShippingSize());
		$this->writeElement('SHIPPING_WEIGHT', $dto->getShippingWeight());
		$this->writeElement('SHOP_PAIRING_ID', $dto->getShopPairingId());
		$this->writeElement('SHOP_PAIRING_PARENT_ID', $dto->getShopPairingParentId());
		$this->writeElement('STOCK_QUANTITY', $dto->getStockQuantity());
		$this->writeElement('SUPPLIER', $dto->getSupplier());
		$this->writeElement('VAT', $dto->getVat());
		$this->writeElement('VIDEO', $dto->getVideo());
		$this->writeElement('VIDEO_ALTERNATIVE', $dto->getVideoAlternative());
		// $this->createVoucher($dto);
		$this->writeElement('WARRANTY', $dto->getWarranty());

		$this->getWriter()->endElement();
		$this->getWriter()->flush();
	}

	private function createAttachedProducts(Variant|ClassEvent|OnlineCourseWithoutEvent $dto): void
	{
		if (!$dto->getAttachedProductIds()) {
			return;
		}
		$this->getWriter()->startElement('ATTACHED_PRODUCT');
		foreach ($dto->getAttachedProductIds() as $attachedProductId) {
			$this->writeElement('ITEMGROUP_ID', $attachedProductId);
		}
		$this->getWriter()->endElement();
	}

	private function createImageAlternative(Variant|ClassEvent|OnlineCourseWithoutEvent $dto): void
	{
		if (!$dto->getImages()) {
			return;
		}

		foreach ($dto->getImages() as $image) {
			$this->writeElement('IMAGE_ALTERNATIVE', $image);
		}
	}

	private function createElementDelivery(Variant|ClassEvent|OnlineCourseWithoutEvent $productDto): void
	{
		if (empty($productDto->getDeliveries())) {
			return;
		}

		foreach ($productDto->getDeliveries() as $delivery) {
			$this->getWriter()->startElement('DELIVERY');
			$this->writeElement('ID', $delivery['id']);
			$this->writeElement('PRICE', $delivery['price']);
			$this->writeElement('PRICE_COD', $delivery['pickupPersonalPrice']);
			$this->getWriter()->endElement();
		}
	}

	private function createDepot(Variant|ClassEvent|OnlineCourseWithoutEvent $productDto): void
	{
		if (empty($productDto->getDepots())) {
			return;
		}

		foreach ($productDto->getDepots() as $depot) {
			$this->getWriter()->startElement('DEPOT');
			$this->writeElement('NAME', $depot['name']);
			$this->writeElement('STOCK_QUANTITY', $depot['stockQuantity']);
			$this->getWriter()->endElement();
		}
	}

	private function createParams(Variant|ClassEvent|OnlineCourseWithoutEvent $productDto): void
	{
		if (empty($productDto->getParams())) {
			return;
		}

		foreach ($productDto->getParams() as $param) {
			$this->getWriter()->startElement('PARAM');
			$this->writeElement('NAME', $param['name']);
			$this->writeElement('VALUE', $param['value']);
			$this->getWriter()->endElement();
		}
	}

	public function getRootElementAttributes(): array
	{
		return [
			'xmlns' => 'http://www.mergado.com/ns/1.11',
		];
	}

	public function getElements(): array
	{
		return [
			'LINK' => 'http://www.mergado.com/ns/1.11',
			'GENERATOR' => 'mergado.woocommerce.marketingpack.3_7_3',
		];
	}

	private function getItems(int $limit, int $batch): ?array
	{
		$mutation = $this->orm->mutation->getByCode($this->getDispatcher()->getProvider()->getMutationCode());

		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$setup = new Setup(
			$mutation,
			$mutation->getFirstState(),
			$mutation->getDefaultPriceLevel(),
			null
		);

		$this->translatorDB->setMutation($mutation);

		$productLocalizations = $this->orm->productLocalization->findBy([
				'mutation' => $mutation,
			])
			->orderBy('id')
			->limitBy($limit, $batch * $limit)
			->fetchAll();

		if ($productLocalizations === [] || $productLocalizations === null) {
			return null;
		}

		$items = [];

		foreach ($productLocalizations as $productLocalization) {
			assert($productLocalization instanceof ProductLocalization);
			if ($productLocalization->product->isOnlineCourseWithoutEvent()) {
				$items[] = OnlineCourseWithoutEvent::make(
					$productLocalization,
					$productLocalization->product->firstVariant,
					$setup,
					$this->orm
				)
				->setPrefix('online-course-without-event-');
			} elseif ($productLocalization->product->isCourse()) {
				foreach ($productLocalization->product->classEvents as $classEvent) {
					$items[] = ClassEvent::make(
						$productLocalization,
						$classEvent,
						$setup,
						$this->orm,
						$this->translatorDB
					)
					->setPrefix('class-event-');
				}
			} else {
				foreach ($productLocalization->product->variants as $productVariant) {
					$items[] = Variant::make(
						$productLocalization,
						$productVariant,
						$setup,
						$this->orm,
						$this->currentDateTimeProvider
					)
					->setPrefix('product-variant-');
				}
			}
		}

		return $items;
	}

}
