<?php

declare(strict_types=1);

namespace App\Model\FeedGenerator\Individual\DTO\Mergado;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Payment\PaymentType;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Setup;
use App\PostType\Page\Model\Orm\CatalogTree;
use ArrayIterator;
use Brick\Money\Currency;

class OnlineCourseWithoutEvent extends DTO implements DTOInterface
{

	private string $prefix = '';

	private ProductVariant $productVariant;

	private ProductLocalization $productLocalization;

	private Setup $setup;

	private Orm $orm;

	public static function make(
		ProductLocalization $productLocalization,
		ProductVariant $productVariant,
		Setup $setup,
		Orm $orm
	): OnlineCourseWithoutEvent
	{
		$instance = new static();

		$instance->productVariant = $productVariant;
		$instance->productLocalization = $productLocalization;
		$instance->setup = $setup;
		$instance->orm = $orm;

		return $instance;
	}

	public function getAttachedProductIds(): ?array
	{
		$productIds = $this->productVariant->product->attachedProducts->toCollection()->fetchPairs(null, 'id');

		if ($productIds === [] || $productIds === null) {
			return null;
		}

		return $productIds;
	}

	public function setPrefix(string $prefix): OnlineCourseWithoutEvent
	{
		$this->prefix = $prefix;
		return $this;
	}

	public function getPrefix(): string
	{
		return $this->prefix;
	}

	public function getItemId(): string
	{
		return $this->getPrefix() . $this->getVariantId();
	}

	public function getVariantId(): string
	{
		return (string) $this->productVariant->id;
	}

	public function getProductId(): string
	{
		return (string) $this->productVariant->product->id;
	}

	public function getAccessory(): ?string
	{
		return null; // Not needed
	}

	public function getAdult(): ?string
	{
		return null; // Not needed
	}

	public function getAgeGroup(): ?string
	{
		return null; // Not needed
	}

	public function getAvailability(): ?string
	{
		return 'in stock';
	}

	public function getAvailabilityType(): ?string
	{
		return null; // Not needed
	}

	public function getBenefit(): ?string
	{
		return null; // TODO:Text from Gift
	}

	public function getBrand(): ?string
	{
		return 'DronPro.cz';
	}

	public function getBrandUrl(): ?string
	{
		return 'https://akademie.dronpro.cz';
	}

	public function getCategory(): ?string
	{
		$pathItems = $this->productVariant->product->mainCategory?->getParentsAndSelf();

		if ($pathItems === [] || $pathItems === null) {
			return null;
		}

		$categories = [];

		foreach ($pathItems as $pathItem) {
			if ($pathItem instanceof CatalogTree) {
				$categories[] = $pathItem->name;
			}
		}

		if ($categories === []) {
			return null;
		}

		return implode(' | ', $categories);
	}

	public function getCategoryDescription(): ?string
	{
		return $this->productVariant->product->mainCategory?->annotation;
	}

	public function getCategoryId(): ?int
	{
		return $this->productVariant->product->mainCategory?->id;
	}

	public function getCategoryMaxPriceVat(): ?float
	{
		return $this->productVariant->product->mainCategory?->getMaxPriceVat($this->setup->mutation);
	}

	public function getCategoryMinPriceVat(): ?float
	{
		return $this->productVariant->product->mainCategory?->getMinPriceVat($this->setup->mutation);
	}

	public function getCategoryName(): ?string
	{
		return $this->productVariant->product->mainCategory?->name;
	}

	public function getCategoryQuantity(): ?int
	{
		return $this->productVariant->product->mainCategory?->getProductQuantity($this->setup->mutation);
	}

	public function getCategoryUrl(): ?string
	{
		return $this->productVariant->product->mainCategory?->alias?->getUrl();
	}

	public function getCpc(): ?int
	{
		return null; // Not needed
	}

	public function getCpcFulltext(): ?int
	{
		return null; // Not needed
	}

	public function getColor(): ?string
	{
		return null; // Not needed
	}

	public function getCondition(): ?string
	{
		return null; // Not needed
	}

	public function getConditionDescription(): ?string
	{
		return null; // Not needed
	}

	public function getCost(): ?float
	{
		return null; // Not needed
	}

	public function getCostVat(): ?float
	{
		return null; // Not needed
	}

	public function getCurrency(): string
	{
		return $this->productLocalization->mutation->getSelectedCurrency()->getCurrencyCode();
	}

	public function getCustomLabel(): ?string
	{
		return null; // TODO:??? Which variable should I use? Tags or parameters?
	}

	public function getDeliveryDays(): ?int
	{
		return 0;
	}

	protected function getDeliveryMethods(): array
	{
		return $this->orm->deliveryMethod->getAvailable($this->setup->mutation, $this->setup->state, $this->setup->priceLevel, $this->setup->mutation->currency)->fetchPairs('id', null);
	}

	public function getDeliveries(): ?array
	{
		$deliveryMethods = $this->getDeliveryMethods();
		$mutation = $this->setup->mutation;
		$state = $this->setup->state;
		$priceLevel = $this->setup->priceLevel;
		$currency = $this->setup->mutation->getSelectedCurrency();

		$shipmentPrices = [];
		/** @var DeliveryMethodConfiguration $deliveryMethod */
		foreach ($deliveryMethods as $deliveryMethod) {
			if (!$deliveryMethod->getDeliveryMethod()->isAvailableForProduct($this->productVariant->product)) {
				continue;
			}

			$deliveryPrice = $deliveryMethod->getPrice($priceLevel, $state, $currency);

			$productPrice = $this->productVariant->price($mutation, $priceLevel, $state);

			$isDeliveryFree = $deliveryPrice->freeFrom !== null && $productPrice->getAmount()->isGreaterThan($deliveryPrice->freeFrom);

			$shipmentPrices[] = [
				'id' => $deliveryMethod->name,
				'price' => ($this->productVariant->product->hasFreeTransport() || $isDeliveryFree) ? 0.0 : $deliveryPrice->getPrice()->getAmount()->toFloat(),
				'pickupPersonalPrice' => $this->getPickUpPrice($deliveryMethod, $mutation, $state, $priceLevel, $currency),
			];
		}

		return $shipmentPrices;
	}

	private function getPickUpPrice(DeliveryMethodConfiguration $deliveryMethod, Mutation $mutation, State $country, PriceLevel $priceLevel, Currency $currency): ?float
	{
		$pickupPersonalPrices = $this->orm->paymentMethod->getAvailableByDelivery($deliveryMethod, $mutation, $country, $priceLevel, $currency)->findBy(['paymentMethodUniqueIdentifier' => PaymentType::CashOnDelivery]);

		if (!$pickupPersonalPrices->count()) {
			return null;
		}

		$pricePickUp = null;

		foreach ($pickupPersonalPrices as $pickupPersonalPrice) {

			if (!$price = $pickupPersonalPrice->getPrice($priceLevel, $country, $currency)) {
				continue;
			}

			if ($pricePickUp === null) {
				$pricePickUp = $price->getPrice()->getAmount()->toFloat();
				continue;
			}

			if ($price->getPrice()->getAmount()->isLessThan($pricePickUp)) {
				$pricePickUp = $price->getPrice()->getAmount()->toFloat();
			}
		}

		return $pricePickUp;
	}

	public function getDepots(): ?array
	{
		return [
			[
				'name' => 'DronPro.cz',
				'stockQuantity' => 99,
			],
		];
	}

	public function getDescription(): ?string
	{
		return $this->productLocalization->content;
	}

	public function getDescriptionShort(): ?string
	{
		return $this->productLocalization->annotation;
	}

	public function getDues(): ?float
	{
		return null;
	}

	public function getEan(): ?string
	{
		$ean = $this->productVariant->ean;

		if ($ean === 0 || $ean === '0') {
			return null;
		}

		return $ean;
	}

	public function getEnergyClass(): ?string
	{
		return null; // Not needed
	}

	/**
	 * Available values: extended_warranty, free_accessories, free_case, free_delivery, free_gift, free_installation, free_store_pickup, voucher
	 */
	public function getExtraMessage(): ?string
	{
		return null; // Not needed
	}

	public function getGender(): ?string
	{
		return null; // Not needed
	}

	public function getHighlight(): ?bool
	{
		return null; // Not needed
	}

	public function getIdentifierExists(): bool
	{
		return $this->getEan() !== null && $this->getBrand() !== null;
	}

	public function getIsbn(): ?string
	{
		return $this->productVariant->isbn;
	}

	public function getImage(): ?string
	{
		return $this->productLocalization->product->firstImage->url;
	}

	public function getImages(): ?array
	{
		$images = $this->productLocalization->product->images->toCollection();

		if ($images->count() === 0) {
			return null;
		}

		$urls = [];

		foreach ($images as $image) {
			$urls[] = $image->url;
		}

		if ($urls === []) {
			return null;
		}

		return $urls;
	}

	public function getItemGroupId(): ?string
	{
		return $this->getProductId();
	}

	public function getMaterial(): ?string
	{
		return null; // Not needed
	}

	public function getNameExact(): ?string
	{
		return $this->productLocalization->name;
	}

	public function getNameCommercial(): ?string
	{
		return $this->productLocalization->name;
	}

	public function getParams(): ?array
	{
		$parameterValues = $this->productVariant->product->getParametersValues();

		if ($parameterValues->count() === 0) {
			return null;
		}

		$params = [];

		foreach ($parameterValues as $parameterValue) {
			if ($parameterValue instanceof ArrayIterator) {
				foreach ($parameterValue as $value) {
					if ($value->parameter->name === null || $value->value === null) {
						continue;
					}

					$params[] = [
						'name' => $value->parameter->name,
						'value' => $value->value,
					];
				}
			} else {
				if ($parameterValue->parameter->name === null || $parameterValue->value === null) {
					continue;
				}

				$params[] = [
					'name' => $parameterValue->parameter->name,
					'value' => $parameterValue->value,
				];
			}
		}

		if ($params === []) {
			return null;
		}

		return $params;
	}

	public function getPriority(): ?int
	{
		return null; // Not needed
	}

	public function getProducer(): ?string
	{
		return null; // Not needed
	}

	public function getProductNo(): ?string
	{
		return $this->productVariant->code;
	}

	public function getProductType(): ?string
	{
		return null; // Not needed
	}

	public function getReleaseDate(): ?string
	{
		return null; // Not needed
	}

	public function getSize(): ?string
	{
		return null; // Not needed
	}

	public function getUrl(): ?string
	{
		return $this->productLocalization->alias?->getUrl();
	}

	public function getUrlAdwords(): ?string
	{
		return null; // Not needed
	}

	public function getUrlMobile(): ?string
	{
		return null; // Not needed
	}

	public function getPattern(): ?string
	{
		return null; // Not needed
	}

	public function getPrice(): ?float
	{
		$priceInfo = $this->productVariant->getPriceInfo($this->productLocalization->mutation, $this->productLocalization->mutation->getDefaultPriceLevel(), $this->productLocalization->mutation->getFirstState());

		return $priceInfo->getStandardPrice()->getAmount()->toFloat();
	}

	public function getPriceVat(): ?float
	{
		$priceInfo = $this->productVariant->getPriceInfo($this->productLocalization->mutation, $this->productLocalization->mutation->getDefaultPriceLevel(), $this->productLocalization->mutation->getFirstState());

		return $priceInfo->getStandardPriceVat()->getAmount()->toFloat();
	}

	public function getPriceRetail(): ?string
	{
		return null; // Which value should I use?
	}

	public function getPriceRetailVat(): ?string
	{
		return null; // Which value should I use?
	}

	public function getPriceDiscount(): ?float
	{
		$priceInfo = $this->productVariant->getPriceInfo($this->productLocalization->mutation, $this->productLocalization->mutation->getDefaultPriceLevel(), $this->productLocalization->mutation->getFirstState());

		return $priceInfo->getDiscountPrice()?->getAmount()->toFloat();
	}

	public function getPriceDiscountVat(): ?float
	{
		$priceInfo = $this->productVariant->getPriceInfo($this->productLocalization->mutation, $this->productLocalization->mutation->getDefaultPriceLevel(), $this->productLocalization->mutation->getFirstState());

		return $priceInfo->getDiscountPriceVat()?->getAmount()->toFloat();
	}

	public function getShippingLabel(): ?string
	{
		return null; // Which value should I use?
	}

	public function getShippingSize(): ?string
	{
		return null; // Not needed
	}

	public function getShippingWeight(): ?float
	{
		return null; // Not needed
	}

	public function getShopPairingId(): ?string
	{
		return null; // Which value should I use?
	}

	public function getShopPairingParentId(): ?string
	{
		return null; // Which value should I use?
	}

	public function getStockQuantity(): ?int
	{
		return 99;
	}

	public function getSupplier(): ?string
	{
		return null; // Not needed
	}

	public function getVat(): ?int
	{
		$priceInfo = $this->productVariant->getPriceInfo($this->productLocalization->mutation, $this->productLocalization->mutation->getDefaultPriceLevel(), $this->productLocalization->mutation->getFirstState());

		return $priceInfo->getVatRate()->toInt();
	}

	public function getVideo(): ?string
	{
		return null; // Not needed
	}

	public function getVideoAlternative(): ?string
	{
		return null; // Not needed
	}

	public function getWarranty(): ?string
	{
		return null; // Not needed
	}

}
