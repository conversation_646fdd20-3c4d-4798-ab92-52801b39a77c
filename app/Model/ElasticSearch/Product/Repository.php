<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\BucketFilter\ElasticItem\Fulltext;
use App\Model\BucketFilter\ElasticItem\IsNotOld;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\Sort;
use App\Model\Currency\CurrencyHelper;
use App\Model\ElasticSearch\IndexModel;
use App\Model\ElasticSearch\Product\AggregationParser\MenuMeta\TopProductParser;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use Elastica\Aggregation\Terms;
use Elastica\Query;
use Elastica\Query\AbstractQuery;
use Elastica\Query\BoolQuery;
use Elastica\Query\Range;
use Elastica\QueryBuilder;
use Elastica\Result;
use Elastica\ResultSet;
use Nextras\Orm\Collection\ICollection;

class Repository extends \App\Model\ElasticSearch\Repository
{

	public const MINIMUM_SCORE = 10;

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		IndexModel $indexModel,
		private readonly ResultReader $productResultReader,
		private readonly TopProductParser $topProductParser,
	)
	{
		parent::__construct($indexModel);
	}


	public function searchByFilter(Mutation $mutation, CatalogTree $catalogTree, array $filter, int $size = 1): ResultSet
	{
		$query = new Query();
		$query->setSize($size)
			->setFrom(0);

		$b = new QueryBuilder();

		$must = [];
		$must = $this->addOnlyPublic($b, $must);

		$must = $this->addInPath($catalogTree, $must);

		if ($must !== []) {
			$bool = $b->query()->bool();
			foreach ($must as $item) {
				$bool->addMust($item);
			}

			$query->setQuery($bool);
		}

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);
	}

	public function fulltextSearch(Mutation $mutation, string $textToSearch, int $limit, array $forcedIds = []): \App\Model\BucketFilter\Result
	{
		$query = new Query();
		$query->setSize($limit)
			->setSort(['_score' => 'desc', 'stockPriority' => 'desc'])
			->setFrom(0);

		if ($forcedIds === []) {
			$query->setMinScore(self::MINIMUM_SCORE);
		}

		$b = new QueryBuilder();

		$boolQuery = $b->query()->bool()->addMust(
			$this->getInPath($mutation->pages->eshop)
		)->addMust(
			$this->getOnlyPublic()
		)->addMust(
			(new Fulltext($textToSearch, $forcedIds))->getCondition()
		)->addMust(
			(new IsNotOld())->getCondition()
		);

		$query->setQuery($boolQuery);
		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		if ($esIndex === null) {
			return \App\Model\BucketFilter\Result::empty();
		}

		$esResults = $this->baseSearch($esIndex, $query);
		$products = $this->productResultReader->mapResultToEntityCollection($esResults);


		return \App\Model\BucketFilter\Result::from(
			$products,
			$products->count(),
			$esResults->getTotalHits(),
		);
	}

	/**
	 * @param QueryBuilder $b
	 * @param array $must
	 * @return array
	 */
	private function addOnlyPublic(QueryBuilder $b, array $must): array
	{
// !!!
		$must[] = $b->query()->term(['isPublic' => true]);
		$publicFrom = new Range('publicFrom', [
			'lte' => 'now',
		]);
		$must[] = $publicFrom;

		$publicTo = new Range('publicTo', [
			'gte' => 'now',
		]);
		$must[] = $publicTo;
		return $must;
	}
	private function getOnlyPublic(): BoolQuery
	{
		$b = new QueryBuilder();
		return $b->query()->bool()->addMust(
			new Range('publicFrom', ['lte' => 'now'])
		)->addMust(
			new Range('publicTo', ['gte' => 'now'])
		)->addMust(
			$b->query()->term(['isPublic' => true])
		);
	}

	/**
	 * @param CatalogTree $catalogTree
	 * @param array $must
	 * @return array
	 */
	private function addInPath(CatalogTree $catalogTree, array $must): array
	{
		$must[] = new Query\Term(['path' => $catalogTree->id]);
		return $must;
	}
	private function getInPath(CatalogTree $catalogTree): AbstractQuery
	{
		return new Query\Term(['path' => $catalogTree->id]);
	}


	private function addIsInStore(array $must): array
	{
		$b = new QueryBuilder();

		$must[] = $b->query()->term(['isInStore' => true]);
		return $must;
	}

	public function findByCategoryAndMustArray(CatalogTree $category, array $must, Sort $esSort, int $limit = 16): ResultSet
	{
		$query = new Query();
		$query->setSize($limit)
			->setFrom(0)
			->setSort($esSort->getSentences());

		$b = new QueryBuilder();

		$must = $this->addOnlyPublic($b, $must);
		$must = $this->addInPath($category, $must);
		$must = $this->addIsInStore($must);

		$queryBool = $b->query()->bool();
		foreach ($must as $item) {
			$queryBool->addMust($item);
		}

		$query->setQuery($queryBool);

		$esIndex = $this->esIndexRepository->getProductLastActive($category->mutation);
		return $this->baseSearch($esIndex, $query);
	}

	public function findByIds(array $ids, Mutation $mutation, bool $onStockOnly, ?int $count = null, int $from = 0, bool $inPath = true): ResultSet
	{
		$query = new Query();
		$b = new QueryBuilder();

		if (isset($count)) {
			$query->setFrom($from)->setSize($count);
		} else {
			$query->setSize(30);
		}

		$must = [];
		$must = $this->addOnlyPublic($b, $must);
		if ($onStockOnly) {
			$must = $this->addIsInStore($must);
		}

		$must = $this->addHasId($ids, $must);
		if ($inPath) {
			$must = $this->addInPath($mutation->pages->eshop, $must);
		}

		$bool = $b->query()->bool();
		foreach ($must as $item) {
			$bool->addMust($item);
		}

		$query->setQuery($bool);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->baseSearch($esIndex, $query);
	}

	/**
	 * @return ICollection<Product>
	 */
	public function findOnStock(Mutation $mutation, ?array $ids = [], ?int $limit = null, int $offset = 0): ICollection
	{
		$query = new Query();
		$b = new QueryBuilder();

		if ($limit !== null) {
			$query->setFrom($offset)->setSize($limit);
		} else {
			$query->setSize(30);
		}

		$must = [];

		$must = $this->addOnlyPublic($b, $must);

		if ($ids !== null) {
			$must = $this->addHasId($ids, $must);
		}

		$must[] = new Query\Terms('availability', array_values(CustomProductAvailability::getOnStockTypes()));

		$bool = $b->query()->bool();
		foreach ($must as $item) {
			$bool->addMust($item);
		}

		$query->setQuery($bool);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		return $this->productResultReader->mapResultToEntityCollection($this->baseSearch($esIndex, $query));
	}

	private function addHasId(array $ids, array $must): array
	{
		$b = new QueryBuilder();

		$must[] = $b->query()->terms('id', array_values($ids));
		return $must;
	}


	public function getMainMenuMetadata(Mutation $mutation, array $pathIds): array
	{
		$pathIds = array_values($pathIds);
		$mainCatalogPage = $mutation->pages->eshop;
		$query = new Query();
		$b = new QueryBuilder();

		$query->setSize(1);

		$must = [];
		$must = $this->addOnlyPublic($b, $must);
		$must = $this->addInPath($mainCatalogPage, $must);


		$bool = $b->query()->bool();

		foreach ($must as $item) {
			$bool->addMust($item);
		}
		$query->setQuery($bool);

		$filterTopProduct = $b->query()->bool();

		$query->addAggregation(
			$b->aggregation()->terms('inPathAgg')->setField('path')->setIncludeAsExactMatch($pathIds)
				->addAggregation(
					$b->aggregation()->filter('topProductFiltered', $filterTopProduct)->addAggregation(
						$b->aggregation()->top_hits('topProductByScore')->setSort(['score' => 'desc'])->setSize(1)
					)
				)
		);
		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);

		$topProductByCategoryIds = [];

		if ($esIndex !== null) {
			$resultSet = $this->baseSearch($esIndex, $query);
			if (isset($resultSet->getAggregation('inPathAgg')['buckets'])) {
				foreach ($resultSet->getAggregation('inPathAgg')['buckets'] as $inPathAgg) {
					$categoryId = $inPathAgg['key'];
					$topProductByCategoryIds[$categoryId] = $this->topProductParser->parse($inPathAgg);

				}
			}
		}

		return [$topProductByCategoryIds];
	}

	final public function findAllByMainCategory(Tree $category, Mutation $mutation, ?int $limit = 10, ?int $offset = 0, array $sort = []): ResultSet
	{
		$builder = new QueryBuilder();
		$mainCategory = $builder->query()->match('mainCategory', $category->id);
		$query = $builder->query()->bool()->addMust($mainCategory);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);

		return $this->findByQuery($esIndex, $query, $limit, $offset, true, $sort);
	}

	public function findAllPublicForFeed(Mutation $mutation, CatalogTree $catalogTree, ?int $limit = 10, ?int $offset = 0): ResultSet
	{
		$builder = new QueryBuilder();
		$isActive = $builder->query()->match('isOld', false);

		$boolQuery = $builder->query()->bool()
			->addMust($isActive);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);

		$must = [];

		$must[] = $builder->query()->term(['hasCustomFeedData' => true]);
		$must = $this->addInPath($catalogTree, $must);
		$must = $this->addOnlyPublic($builder, $must);

		foreach ($must as $mustTerm) {
			$boolQuery->addMust($mustTerm);
		}

		$query = new Query();
		$query->setSize($limit);
		$query->setFrom($offset);
		$query->setQuery($boolQuery);

		return $this->baseSearch($esIndex, $query);
	}

	public function findByPrefix(EsIndex $esIndex, string $prefix, ?int $limit = 10, ?int $offset = 0): ResultSet
	{
		$b = new QueryBuilder();
		$query = $b->query()->match('name', ['query' => $prefix]);
		return $this->findByQuery($esIndex, $query, $limit, $offset);
	}

	public function findTopTagsIds(TagLocalization $tagLocalization, int $limit): array
	{
		$esIndex = $this->esIndexRepository->getProductLastActive($tagLocalization->mutation);
		if ($esIndex === null) {
			return [];
		}
		$b = new QueryBuilder();

		$must = [
			$b->query()->term(['tags' => $tagLocalization->tag->id])
		];
		$must = $this->addOnlyPublic($b, $must);
		$must = $this->addInPath($tagLocalization->mutation->pages->eshop, $must);

		$bollQuery = $b->query()->bool();
		foreach ($must as $item) {
			$bollQuery->addMust($item);
		}

		$query = new Query();
		$query->setSize($limit);
		$query->setFrom(0);
		$query->setQuery($bollQuery);
		$query->setSort(['score' => 'desc']);
		$query->setFieldDataFields(['score']);

		$results = $this->baseSearch($esIndex, $query);

		return array_map(fn(Result $result) => $result->getId(), $results->getResults());
	}

	public function getUserMainMenuMetadata(Mutation $mutation, array $pathIds, array $userTopProductIds): array
	{
		$pathIds = array_values($pathIds);
		$userTopProductIds = array_values($userTopProductIds);
		$mainCatalogPage = $mutation->pages->eshop;
		$query = new Query();
		$b = new QueryBuilder();

		$query->setSize(1);

		$must = [];
		$must = $this->addOnlyPublic($b, $must);
		$must = $this->addInPath($mainCatalogPage, $must);

		$must[] = $b->query()->terms('path', $pathIds);
		$must[] = $b->query()->terms('similarSaleProductIds', $userTopProductIds);

		$bool = $b->query()->bool();

		foreach ($must as $item) {
			$bool->addMust($item);
		}
		$query->setQuery($bool);

		$filterTopProduct = $b->query()->bool();

		$query->addAggregation(
			$b->aggregation()->terms('inPathAgg')->setField('path')
				->addAggregation(
					$b->aggregation()->filter('topProductFiltered', $filterTopProduct)->addAggregation(
						$b->aggregation()->top_hits('topProductByScore')->setSort(['score' => 'desc'])->setSize(10)
					)
				)
		);
		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);

		$userTopProductByCategoryIds = [];

		if ($esIndex !== null) {
			$resultSet = $this->baseSearch($esIndex, $query);
			if (isset($resultSet->getAggregation('inPathAgg')['buckets'])) {
				foreach ($resultSet->getAggregation('inPathAgg')['buckets'] as $inPathAgg) {
					$categoryId = $inPathAgg['key'];
					$userTopProductByCategoryIds[$categoryId] = $this->topProductParser->parse($inPathAgg);
				}
			}
		}

		return $userTopProductByCategoryIds;
	}


    public function findTopProduct(Mutation $mutation, ?int $limit = 10): \App\Model\BucketFilter\Result
    {
		$b = new QueryBuilder();
		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		if ($esIndex === null) {

			return \App\Model\BucketFilter\Result::empty();
		}

		$sort = ['score' => 'desc'];
		$query = $b->query()->bool()->addMust(
			$this->getOnlyPublic()
		)->addMust(
			$this->getInPath($mutation->pages->eshop)
		);

		$esResultSet = $this->findByQuery($esIndex, $query, $limit, sort: $sort);

		$productCollection = $this->productResultReader->mapResultToEntityCollection($esResultSet);

		return \App\Model\BucketFilter\Result::from(
			$productCollection,
			$productCollection->count(),
			$esResultSet->getTotalHits(),
		);

    }


	public function getProductCountsForCategories(Mutation $mutation, array $categoryIds): array
	{
		$query = new Query();
		$query->setSize(0);

		$qb  = new QueryBuilder();
		$qry = $qb->query()->bool();
		$qry->addMust((new IsPublic())->getCondition());
		$qry->addMust($qb->query()->terms('path', $categoryIds));
		$query->setQuery($qry);

		$categoryAggregation = new Terms('catAgg');
		$categoryAggregation->setField('path');
		$categoryAggregation->setSize(2000)->setIncludeAsExactMatch($categoryIds);
		$query->addAggregation($categoryAggregation);

		$categoryProductCount = [];
		$esIndex  = $this->esIndexRepository->getProductLastActive($mutation);
		if ($esIndex !== null) {
			$esResult = $this->baseSearch($esIndex, $query);
			$categoryAggBuckets = $esResult->getAggregation('catAgg')['buckets'];
			foreach ($categoryAggBuckets as $categoryAggBucket) {
				$categoryProductCount[$categoryAggBucket['key']] = $categoryAggBucket['doc_count'];
			}
		}

		return $categoryProductCount;
	}

	protected function getBoolQuery(array $must): AbstractQuery
	{
		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();
		foreach ($must as $item) {
			$boolQuery->addMust($item);
		}

		return $boolQuery;
	}

	public function getMaxAndMinPricesForCategory(Mutation $mutation, CatalogTree $catalogTree): ?ResultSet
	{
		$builder = new QueryBuilder();
		$must[] = (new IsPublic())->getCondition();
		$must[] = $builder->query()->terms('path', [$catalogTree->id]);

		$boolQuery = $this->getBoolQuery($must);

		$esIndex = $this->esIndexRepository->getProductLastActive($mutation);

		if ($esIndex === null) {
			return null;
		}

		$minAgg = $builder->aggregation()
			->min('minPriceAgg')
			->setField('statePricesWithVat.'.$mutation->getFirstState()->code.'.default.'.$mutation->getSelectedCurrency()->getCurrencyCode());

		$maxAgg = $builder->aggregation()
			->max('maxPriceAgg')
			->setField('statePricesWithVat.'.$mutation->getFirstState()->code.'.default.'.$mutation->getSelectedCurrency()->getCurrencyCode());

		return $this->findByQuery($esIndex, $boolQuery, 0, aggregations: [$minAgg, $maxAgg]);
	}

}
