<?php
declare(strict_types=1);

namespace App\Model\Messenger\Transport;


use App\Exceptions\LogicException;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Result\Row;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Stamp\TransportMessageIdStamp;
use Symfony\Component\Messenger\Transport\Receiver\ListableReceiverInterface;
use Symfony\Component\Messenger\Transport\Receiver\MessageCountAwareInterface;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;
use Symfony\Component\Messenger\Transport\TransportInterface;

readonly class DbalTransport implements TransportInterface, ListableReceiverInterface, MessageCountAwareInterface
{
	private const string STATUS_NEW = 'new';
	private const string STATUS_PROCESSING = 'processing';
	private const string STATUS_HANDLED = 'handled';
	public function __construct(
		private Connection $connection,
		private SerializerInterface $serializer,
		private string $queueName,
		private string $tableName,
	)
	{
	}

	public function get(): iterable
	{
		$row = $this->connection->query('SELECT * FROM %table WHERE queueName = %s AND status = %s', $this->tableName, $this->queueName, self::STATUS_NEW)->fetch();
		if ($row !== null) {
			$this->connection->query('UPDATE %table SET status = %s WHERE id = %i', $this->tableName, self::STATUS_PROCESSING, $row->id);
			return [$this->createEnvelopeFromData($row)];
		}
		return [];
	}

	public function ack(Envelope $envelope): void
	{
		$stamp = $envelope->last(TransportMessageIdStamp::class);
		if (!$stamp instanceof TransportMessageIdStamp) {
			throw new LogicException('No TransportMessageIdStamp found on the Envelope.');
		}

		$this->connection->query('UPDATE %table SET status = %s, ackedAt = %dt WHERE id = %i', $this->tableName, self::STATUS_HANDLED, new DateTimeImmutable(), $stamp->getId());
	}

	public function reject(Envelope $envelope): void
	{
		$stamp = $envelope->last(TransportMessageIdStamp::class);
		if (!$stamp instanceof TransportMessageIdStamp) {
			throw new LogicException('No TransportMessageIdStamp found on the Envelope.');
		}
		$this->connection->query('DELETE FROM %table WHERE id = %i', $this->tableName, $stamp->getId());
	}

	public function send(Envelope $envelope): Envelope
	{
		$encodedMessage = $this->serializer->encode($envelope);

		$this->connection->query('INSERT INTO %table %values', $this->tableName, [
				'body' => $encodedMessage['body'],
				'headers' => $encodedMessage['headers'] ?? '',
				'createdAt' => new \DateTimeImmutable(),
				'status' => self::STATUS_NEW,
				'queueName' => $this->queueName,
			]
		);
		$id = $this->connection->getLastInsertedId();

		return $envelope->with(new TransportMessageIdStamp($id));
	}

	public function all(?int $limit = null): iterable
	{
		foreach ($this->connection->query('SELECT * FROM %table WHERE queueName = %s AND status = %s', $this->tableName, $this->queueName, self::STATUS_NEW) as $row) {
			yield $this->createEnvelopeFromData($row);
		};
	}

	public function find(mixed $id): ?Envelope
	{
		$row = $this->connection->query('SELECT * FROM %table WHERE id = %i AND queueName = %s',$this->tableName, $id, $this->queueName)->fetch();
		if ($row === null) {
			return null;
		}

		return $this->createEnvelopeFromData($row);
	}

	private function createEnvelopeFromData(Row $row): Envelope
	{
		$envelope = $this->serializer->decode([
			'body' => $row->body,
			'headers' => $row->headers,
		]);

		return $envelope->withoutAll(TransportMessageIdStamp::class)->with(new TransportMessageIdStamp($row->id));
	}

	public function getMessageCount(): int
	{
		return $this->connection->query('SELECT COUNT(*) as num FROM %table WHERE queueName = %s AND status = %s', $this->tableName, $this->queueName, self::STATUS_NEW)->fetch()?->num ?? 0;
	}
}
