<?php
declare(strict_types=1);

namespace App\Model\Messenger\Transport;

use Nextras\Dbal\Connection;
use Symfony\Component\Messenger\Transport\Serialization\SerializerInterface;
use Symfony\Component\Messenger\Transport\TransportFactoryInterface;
use Symfony\Component\Messenger\Transport\TransportInterface;

/**
 * @implements TransportFactoryInterface<DbalTransport>
 */
class DbalTransportFactory implements TransportFactoryInterface
{
	public function __construct(private readonly Connection $connection)
	{
	}
	public function createTransport(
		#[\SensitiveParameter] string $dsn,
		array $options,
		SerializerInterface $serializer
	): TransportInterface {
		$tableName = $options['table'] ?? 'failed_messages';
		$queueName = $this->getQueueName($dsn);


		return new DbalTransport($this->connection, $serializer, $queueName, $tableName);
	}

	public function getQueueName(string $dsn): string
	{
		$queueName = str_replace('dbal://', '', $dsn);
		if ($queueName === '') {
			$queueName = 'un';
		}
		dumpe($dsn, $parsed);
		return [];
	}

	public function supports(#[\SensitiveParameter] string $dsn, array $options): bool
	{
		return str_starts_with($dsn, 'dbal://');
	}
}
