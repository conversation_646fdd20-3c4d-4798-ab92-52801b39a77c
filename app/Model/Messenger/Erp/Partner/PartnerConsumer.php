<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Partner;

use App\Model\Erp\Exception\SkippedException;
use App\Model\Messenger\Erp\ImportConsumer;
use App\Model\Odoo\Entity\Partner;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use Nette\Utils\Validators;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class PartnerConsumer extends ImportConsumer
{

	private ?User $user = null;

	public function __construct(
		private readonly UserModel $userModel,
	)
	{
	}

	protected function setup(): void
	{
	}

	public function __invoke(PartnerMessage $message): void
	{
		$this->checkMessageAndImport($message);
	}
	protected function doRemove(): void
	{
		if (($user = $this->orm->user->getBy(['extId' => $this->importCacheOdoo->extId])) !== null) {
			$this->userModel->delete($user);

		}
	}
	protected function doImport(): void
	{
		$this->user = $this->orm->user->getBy(['extId' => $this->importCacheOdoo->extId]);

		$odooEmail = $this->importCacheOdoo->data->email ?? null;
		if ($odooEmail === '') {
			$odooEmail = null;
		}

		if ($odooEmail === null) {
			throw new SkippedException('E-mail is null or empty.');
		}

		if (!Validators::isEmail($odooEmail)) {
			throw new SkippedException('E-mail is not valid e-mail address [' . $odooEmail . '].');
		}

		if ($this->user === null) {
			$userByEmail = $this->orm->user->getBy(['email' => $odooEmail]);
			if ($userByEmail !== null) {
				if ($userByEmail->extId !== null) {
					throw new SkippedException('User with email already exists and has extId.');
				}

				$userByEmail->extId = $this->importCacheOdoo->extId;
				$this->user = $userByEmail;
				$this->update();
			} else {
				$this->create();
			}
		} else {
			$this->update();
		}
	}

	private function update(): void
	{
		$odooEntity = new Partner($this->user);
		$odooEntity->updateOrmEntity($this->user, $this->importCacheOdoo->data); // @phpstan-ignore-line
	}

	private function create(): void
	{
		$ormEntity = $this->userModel->create($this->defaultMutation, $this->importCacheOdoo->data->email, extId: $this->importCacheOdoo->extId, apiRequest: true);
		$odooEntity = new Partner($ormEntity);
		$odooEntity->updateOrmEntity($ormEntity, $this->importCacheOdoo->data);

		assert($ormEntity instanceof User);
		$this->user = $ormEntity;
	}

	protected function save(): void
	{
		if ($this->user !== null) {
			$this->orm->user->persistAndFlush($this->user);
		}
		$this->orm->flush();
		// Do some stuff before importCacheChange persists
		parent::save();
	}

}
