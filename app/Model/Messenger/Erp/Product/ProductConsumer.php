<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Product;

use App\Model\ElasticSearch\All\Facade;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Messenger\Erp\ImportConsumer;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterModel;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationModel;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\ProductType\ProductType;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\String\StringModel;
use App\Model\Orm\SynchronizablePostTypeModel;
use App\Model\TranslatorDB;
use App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalizationModel;
use App\PostType\Tag\Model\Orm\Tag\TagModel;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Tracy\Debugger;
use Tracy\ILogger;

#[AsMessageHandler]
final class ProductConsumer extends ImportConsumer
{

	public const LIBRARY_TREE_CATEGORY = 5;
	public const LIBRARY_PACKAGE_CONTENT = 1000;

	private ?Product $product = null;

	private array $availableLocalizations = [];

	private bool $syncImages = false;

	public function __construct(
		protected readonly Facade $esAllFacade,
		protected readonly \App\Model\ElasticSearch\Product\Facade $esProductFacade,
		protected readonly \App\Model\ElasticSearch\Common\Facade $esCommonFacade,
		protected readonly LibraryImageModel $libraryImageModel,
		protected readonly ProductLocalizationModel $productLocalizationModel,
		//		protected readonly TranslatorDB $translator,
		protected readonly ParameterValueModel $parameterValueModel,
		protected readonly ParameterModel $parameterModel,
		protected readonly ProductModel $productModel,
		protected readonly TagModel $tagModel,
		protected readonly StringModel $stringModel,
		protected readonly BrandLocalizationModel $brandLocalizationModel,
		protected readonly TranslatorDB $translator,
	)
	{
	}

	protected function setup(): void
	{
		// TODO: Implement setup() method.
	}

	public function __invoke(ProductMessage $message): void
	{
		$this->checkMessageAndImport($message);
	}

	protected function getDefaultProductName(): string
	{
		$nameData = (array) $this->importCacheOdoo->data->name;
		return $nameData[Mutation::DEFAULT_ISO_CODE] ?? reset($nameData) ?? '';
	}

	protected function getProduct(): Product
	{
		if (($product = $this->orm->product->getByExtId($this->importCacheOdoo->extId)) === null) {
			$product = new Product();
			$this->orm->product->attach($product);
			$product->internalName = $this->getDefaultProductName();
			$product->extId = $this->importCacheOdoo->extId;
		}
		return $product;
	}

	protected function doRemove(): void
	{
		$product = $this->orm->product->getByExtId($this->importCacheOdoo->extId);
		if ($product === null) {
			throw new SkippedException('Product with extId ' . $this->importCacheOdoo->extId . ' not found.');
		}
		$this->orm->removeAndFlush($product);
	}

	protected function doImport(): void
	{
		throw new UniqueConstraintViolationException('test');
		$this->availableLocalizations = array_keys((array) $this->importCacheOdoo->data->published);

		$this->product = $this->getProduct();
		$this->syncProduct();

		$this->productModel->normalizeProduct($this->product);

		$this->orm->product->persist($this->product);

		$this->syncVariant();
		$this->syncLocalizations();

		$this->productModel->normalizeProduct($this->product);

		$this->syncUsp();
		$this->syncImages();

		$this->syncAlternativeProducts();

		$this->syncGifts();

		$this->tagModel->checkTagsForProduct($this->product);
	}

	protected function syncProduct(): void
	{
		$this->product->productType = match ($this->importCacheOdoo->data->product_type) {
			'consu' => $this->orm->productType->getBy(['uid' => ProductType::UID_PRODUCT]),
			'service' => $this->orm->productType->getBy(['uid' => ProductType::UID_CLASS]),
			default => null
		};

		unset($this->importCacheOdoo->data->extId);

		$this->product->isOld = (int) (!$this->importCacheOdoo->data->active);
		$this->product->public = 1;
		$this->product->dateCreated = $this->importCacheOdoo->data->create_date ?? new DateTimeImmutable();
		$this->product->internalName = $this->getDefaultProductName();
		$this->product->syncTime = new DateTimeImmutable();
		$this->product->syncChecksum = $this->createChecksum($this->importCacheOdoo->data);
	}
	protected function syncLocalizations(): void
	{
		foreach ($this->availableLocalizations as $mutationCode) {

			$mutation = $this->mutations[$mutationCode] ?? null;
			if ($mutation === null) {
				$this->logger?->error('Mutation not found', ['code' => $mutationCode]);
				continue;
			}

			$defaultName         = $this->importCacheOdoo->data->name[$mutationCode] ?? $this->product->internalName; //$this->product->internalName;
			$productLocalization = $this->product->productLocalizations->toCollection()->getBy(['mutation' => $mutation]);
			$isNameChanged       = Strings::webalize($defaultName) !== Strings::webalize($productLocalization?->name ?? '');
			if ($productLocalization === null) {

				$productLocalization           = new ProductLocalization();
				$productLocalization->product  = $this->product;
				$productLocalization->mutation = $mutation;

				$this->orm->productLocalization->persistAndFlush($productLocalization);

				$alias = $defaultName;

				$productLocalization->setAlias($alias);
				$isNameChanged = false;
			}

			$productLocalization->name       = $defaultName;
			$productLocalization->nameAnchor = $defaultName;
			$productLocalization->nameTitle  = $defaultName;
			$productLocalization->annotation    = $this->importCacheOdoo->data->description_ecommerce[$mutationCode] !== false ? $this->importCacheOdoo->data->description_ecommerce[$mutationCode] : '';

			$productLocalization->public = (int) ($this->importCacheOdoo->data->published[$mutationCode] ?? false); //in_array($mutationCode, (array) $this->importCacheOdoo->data->published));
			$this->orm->productLocalization->persistAndFlush($productLocalization);

			if ($isNameChanged) {
				$productLocalization->setAlias($defaultName);
			}

			$this->syncRequalificationPossibility($productLocalization, $mutationCode);
			$this->syncCategories($productLocalization);
			$this->syncPackageContents($productLocalization, $mutationCode);
			$this->syncParameters($productLocalization, $mutationCode);
			$this->syncFaqs($productLocalization, $mutationCode);

			$this->orm->productLocalization->persistAndFlush($productLocalization);
		}
	}

	private function syncRequalificationPossibility(ProductLocalization $productLocalization, string $mutationCode): void
	{
		$importedRequalificationPossibility = $this->importCacheOdoo->data->requalification_possibility[$mutationCode] ?? null;
		if (is_numeric($importedRequalificationPossibility)) {
			$importedRequalificationPossibility = (int) $importedRequalificationPossibility;
		}

		$productLocalization->requalificationPossibility = $importedRequalificationPossibility;

		$requalificationPossibilityToType = 'bez-statniho-prispevku';
		if ($productLocalization->requalificationPossibility !== null) {
			$requalificationPossibilityToType = 'se-statnim-prispevkem';
		}

		if (($requalificationTypePv = $this->product->getRequalificationPossibilityParameterValue())?->internalAlias !== $requalificationPossibilityToType) {
			if ($requalificationTypePv !== null) {
				$this->orm->product->removeParameterValue($this->product, $requalificationTypePv);
			}
			$pv = $this->parameterValueModel->attachParameterValue($this->product, Parameter::UID_REQUALIFICATION_TYPE, $requalificationPossibilityToType);
			$this->orm->product->addParameterValue($this->product, $pv);
		}
	}

	private function syncVariant(): void
	{
		$existingVariantIds = [];
		foreach ($this->importCacheOdoo->data->product_variant_ids as $variantData) {
			$variant = $this->product->variants->toCollection()->getBy(['extId' => $variantData->id]);
			if ($variant === null && $this->product->firstVariant->extId === null) {
				$variant = $this->product->firstVariant;
			}
			if ($variant === null) {
				$variant = new ProductVariant();
				$this->orm->productVariant->attach($variant);
			}

			$variant->code = $variantData->internal_reference ?? '';
			//$variant->ean = $this->importCacheOdoo->data->ean ?? '';
			$variant->extId   = $variantData->id;
			$variant->product = $this->product;

			$this->syncVariantLocalization($variant, $variantData);

			$this->orm->productVariant->persistAndFlush($variant);
			$existingVariantIds[] = $variant->id;
		}

		foreach ($this->product->variants->toCollection()->findBy(['id!=' => $existingVariantIds]) as $variant) {
			$this->orm->productVariant->removeAndFlush($variant);
		}

		//return $variant;
	}

	private function syncVariantLocalization(ProductVariant $variant, ArrayHash $importData): void
	{
		foreach ($this->availableLocalizations as $mutationCode) {

			$mutation = $this->mutations[$mutationCode] ?? null;
			if ($mutation === null) {
				$this->logger?->error('Mutation not found', ['code' => $mutationCode]);
				continue;
			}
			$variantLocalization = $variant->getLocalization($mutation);
			if ($variantLocalization === null) {
				$variantLocalization           = new ProductVariantLocalization();
				$variantLocalization->variant  = $variant;
				$variantLocalization->mutation = $mutation;
			}
			$variantLocalization->active   = (int) ($importData->published[$mutationCode] ?? false); //in_array($mutationCode, (array) $importData->published[$mutation] ?? false);

			if (isset($importData->attributes)) {
				$i = 1;
				foreach ($importData->attributes as $attribute) {
					$attributeName = $attribute->name[$mutationCode] ?? null;
					if ($attributeName === null) {
						continue;
					}
					$parameter = $this->getParameter($mutation, $attribute->extId, $attributeName, variantParameter: 1);

					$attributeValue = $attribute->value[$mutationCode] ?? null;
					if ($attributeValue === null) {
						continue;
					}
					$parameterValue = $this->getParameterValue($mutation, $parameter, $attribute->valueExtId, $attributeValue);
					$parameterValueFunctionName = 'getterParam' . $i . 'ValueId';

					if (method_exists($variant, $parameterValueFunctionName)) {
						$variant->setValue('param' . $i . 'Value', $parameterValue);
					}
					$i++;
				}
				$this->orm->productVariant->persistAndFlush($variant);
			}

		}
	}

	private function syncCategories(ProductLocalization $productLocalization): void
	{
		$categoryExtIds = [];
		$i = 0;
		foreach ($this->importCacheOdoo->data->public_categ_ids as $item) {
			if ($item->language !== $productLocalization->mutation->isoCode) {
				continue;
			}
			$key = $i;
			if ($item->main_category) {
				$key += 1000;
			}
			$categoryExtIds[$key] = $item->extId;
			$i++;
		}

		krsort($categoryExtIds);

		$categories = [];
		$categoriesFound = 0;
		foreach ($this->orm->tree->findByExactOrder($categoryExtIds, 'extId') as $category) {
			$categories[$category->id] = $category->id;
			$categoriesFound++;
		}

		if (count($categoryExtIds) !== $categoriesFound) {
			$this->addWarning('Some categories (' . count($categoryExtIds) . ') not found in DB (' . $categoriesFound . ').');
		}

		// add categories
		$this->productLocalizationModel->attachTo($productLocalization, array_keys($categories));
		$this->orm->persistAndFlush($productLocalization);
	}

	private function syncUsp(): void
	{
		$i = 0;
		foreach ($this->importCacheOdoo->data->unique_selling_proposal_ids as $uspId) {
			foreach ($this->orm->usp->findBy(['extIdRaw' => $uspId]) as $usp) {
				try {
					$productLocalization = $this->product->getLocalization($usp->mutation);
					$cf                  = $productLocalization->customFieldsJson ?? new ArrayHash();

					if ( ! isset($cf->productUsp[$i])) {
						$cf->productUsp[$i] = new \stdClass();
					}

					$cf->productUsp[$i]->uspId = (string) $usp->id;

					$productLocalization->setCf($cf);
					$i++;
					$this->orm->productLocalization->persist($productLocalization);
				} catch (\Throwable $e) {
					Debugger::log($e, ILogger::EXCEPTION);
					$this->addWarning('Failed to update product localization with usp extId (' . $uspId . ').');
				}
			}
		}

		$this->orm->flush();
	}

	private function syncImages(): void
	{
		if (!$this->syncImages) {
			return;
		}
		$productImages = $productImagesToDelete = $this->product->images->toCollection()->fetchPairs('libraryImage->md5');
		$order = 1;
		$hashes = [];

		foreach ($this->importCacheOdoo->data->product_media as $image) {
			$hash = $image->hash;
			if (!array_key_exists($hash, $hashes)) {
				$hashes[$hash] = $image;
			}

		}

		foreach ($hashes as $hash => $image) {
			if (!isset($productImages[$hash])) {
				if (($libraryImage = $this->orm->libraryImage->getBy(['md5' => $hash])) === null) {
					$libraryImage = $this->libraryImageModel->addFromUrl(url: $image->url, cat: self::LIBRARY_TREE_CATEGORY, md5: $hash);

					foreach (array_keys((array) $image->name) as $mutationCode) {
						$mutation = $this->mutations[$mutationCode] ?? null;
						if ($mutation === null) {
							$this->logger?->error('Mutation not found', ['code' => $mutationCode]);
							continue;
						}
						$libraryImage->setAlt($mutation, $image->name[$mutationCode]);
					}
					$this->orm->libraryImage->persistAndFlush($libraryImage);
				}

				if (!$libraryImage->isPersisted()) {
					continue;
				}

				$productImage = new ProductImage();
				$this->orm->productImage->attach($productImage);
				$productImage->libraryImage = $libraryImage->id;
				$productImage->product = $this->product;

			} else {
				/** @var ProductImage $productImage */
				$productImage = $productImages[$hash];
				unset($productImagesToDelete[$hash]);
			}

			$productImage->sort = $order;
			$this->orm->persist($productImage);
			$order++;
		}
		$this->orm->flush();

		foreach ($productImagesToDelete as $productImageToDelete) {
			$this->orm->productImage->removeById($productImageToDelete->id);
		}
	}


	protected function save(): void
	{
		if ($this->product !== null) {
			$this->orm->persistAndFlush($this->product);
			$this->product->flushCache();

			// performance - send update to messenger
			$this->esProductFacade->updateOrDeleteAllMutations($this->product);
			$this->esAllFacade->update($this->product);
		}
		// Do some stuff before importCacheChange persists
		parent::save();
	}

	private function syncGifts(): void
	{
	}

	private function syncAlternativeProducts(): void
	{
		foreach ($this->orm->productProduct->findBy(['type' => ProductProduct::TYPE_NORMAL, 'mainProduct' => $this->product]) as $attachedProduct) {
			$this->orm->productProduct->remove($attachedProduct);
		}
		$this->orm->productProduct->flush();

		if (isset($this->importCacheOdoo->data->alternative_product_ids)) {
			$sort = 0;
			foreach ($this->importCacheOdoo->data->alternative_product_ids as $extId) {
				if (($productVersion = $this->orm->product->getByExtId($extId)) !== null) {
					$updated = $this->orm->productProduct->replace($this->product, $productVersion, ProductProduct::TYPE_NORMAL, $sort);
					$this->orm->productProduct->persist($updated);
					$sort++;
				}
			}
			$this->orm->flush();
		}
	}

	private function syncPackageContents(ProductLocalization $productLocalization, string $mutationCode): void
	{
		$parameter = $this->orm->parameter->getBy(['uid' => Parameter::UID_PRODUCT_COMPLECTATION]);

		$formatExtId = function (int|string $extId): string {
			return 'package_content_' . $extId;
		};

		if (isset($this->importCacheOdoo->data->package_content) && $parameter !== null) {
			$cf = $productLocalization->customFieldsJson ?? new ArrayHash();
			$cf->packageContents = [];

			$packageContentImages = $this->orm->libraryImage->findBy(['library' => self::LIBRARY_PACKAGE_CONTENT])->fetchPairs('md5');

			foreach ($this->importCacheOdoo->data->package_content as $packageContent) {
				$name = $packageContent->component_ext_name[$mutationCode] ?? null;
				if ($name === null) {
					continue;
				}

				$pv = $this->orm->parameterValue->getBy(['parameter' => $parameter, 'extId' => $formatExtId($packageContent->component_extId)]);
				if ($pv === null) {
					$pv = $this->parameterValueModel->createNewValue($parameter, $name);
					$pv->extId = $formatExtId($packageContent->component_extId);
				}

				// ak je cs_CZ upravit internalValue, ak nie pridat preklad
				if ($productLocalization->mutation->isoCode !== Mutation::DEFAULT_ISO_CODE) {
					$this->stringModel->saveTranslation($productLocalization->mutation, $pv->getValueTranslateKey(), $name);
					$this->stringModel->saveTranslation($productLocalization->mutation, $pv->getAliasTranslateKey(), Strings::webalize($name));
				} else {
					$pv->internalValue = $name;
				}

				// nahratie obrazku ak neexistuje k parameter_value
				$hash = $packageContent->image_hash;
				if (!isset($packageContentImages[$hash])) {
					if (($libraryImage = $this->orm->libraryImage->getBy(['md5' => $hash])) === null) {
						$libraryImage = $this->libraryImageModel->addFromUrl(url: $packageContent->image_url, cat: self::LIBRARY_PACKAGE_CONTENT, md5: $hash);
						$this->orm->libraryImage->persistAndFlush($libraryImage);
					}

					if (!$libraryImage->isPersisted()) {
						continue;
					}

				} else {
					$libraryImage = $packageContentImages[$hash];
				}

				if ($libraryImage->isPersisted()) {
					$pvcf                         = $pv->cf ?? new ArrayHash();
					$pvcf->productComplectation   = [];
					$pvcf->productComplectation[] = [
						'image' => $libraryImage->id,
					];
					$pv->cf                       = $pvcf;
				}

				$cf->packageContents[] = [
					'amount' => $packageContent->quantity,
					'parameterValue' => $pv->id,
				];

				$productLocalization->cf = $cf;

				$this->orm->persistAndFlush($pv);
			}

		}
	}

	private function getParameter(Mutation $mutation, int|string $extId, string $name, string $type = Parameter::TYPE_SELECT, int $variantParameter = 0, ?string $uid = null, ?string $unit = null): Parameter
	{
		$parameter = $this->orm->parameter->getBy(['extId' => $extId]);
		if ($parameter === null) {
			$parameter = new Parameter();
			$this->orm->parameter->attach($parameter);
			if ($uid === null) {
				$uid = Strings::webalize($name);
			}
			$parameter->name = $name;
			$parameter->type = $type; //count($values) > 1 ? Parameter::TYPE_MULTISELECT : Parameter::TYPE_SELECT;
			$parameter->uid = $uid;
			$parameter->variantParameter = $variantParameter;
			$parameter->extId = (string) $extId;
			$this->orm->parameter->persistAndFlush($parameter);

			if ($unit !== null) {
				$this->stringModel->saveTranslation($mutation, $parameter->getUnitTranslateKey(), $unit);
			}

		}

		if ($mutation->isoCode === Mutation::DEFAULT_ISO_CODE && $parameter->name !== $name) {
			$parameter->name = $name;
			$this->orm->parameter->persistAndFlush($parameter);
		}

		$this->stringModel->saveTranslation($mutation, $parameter->getTitleTranslateKey(), $name);

		return $parameter;
	}

	private function getParameterValue(Mutation $mutation, Parameter $parameter, int|string $extId, string $valueName): ParameterValue
	{
		$parameterValue = $parameter->options->toCollection()->getBy(['extId' => $extId]);
		if ($parameterValue === null) {
			$parameterValue = $this->parameterValueModel->createNewValue($parameter, $valueName);
			$parameterValue->extId = (string) $extId;
		}

		if ($mutation->isoCode !== Mutation::DEFAULT_ISO_CODE) {
			if ($parameterValue->internalValue !== $valueName) {
				$this->stringModel->saveTranslation($mutation, $parameterValue->getValueTranslateKey(), $valueName);
				$this->stringModel->saveTranslation($mutation, $parameterValue->getAliasTranslateKey(), Strings::webalize($valueName));
			}
		} else {
			$parameterValue->internalValue = $valueName;
		}

		return $parameterValue;
	}

	private function syncParameters(ProductLocalization $productLocalization, string $mutationCode): void
	{
		if (isset($this->importCacheOdoo->data->attributes)) {
			$addedValues = [];
			foreach ($this->importCacheOdoo->data->attributes as $attribute) {
				$name = $attribute->name[$mutationCode] ?? null;
				if ($name === null) {
					continue;
				}

				$extId = $attribute->extId;
				$values = $attribute->values;
				$type = count($values) > 1 ? Parameter::TYPE_MULTISELECT : Parameter::TYPE_SELECT;
				$parameter = $this->getParameter($productLocalization->mutation, $extId, $name, $type);

				foreach ($values as $value) {
					$valueName = $value->value[$mutationCode] ?? null;

					if ($valueName === null) {
						continue;
					}

					$parameterValue = $this->getParameterValue($productLocalization->mutation, $parameter, $value->extId, $valueName);

					if ($parameter->uid === Parameter::UID_BRAND) {
						$this->createOrUpdatePostType($this->brandLocalizationModel, $productLocalization->mutation, $parameterValue);
					}

					if ($mutationCode === Mutation::DEFAULT_ISO_CODE) {
						$addedValues[] = $parameterValue->id;
					}

					$this->parameterValueModel->addValue($parameterValue, $this->product);
					$this->orm->parameterValue->persistAndFlush($parameterValue);
				}
			}

			if ($addedValues !== []) {
				foreach ($this->product->parametersValues->toCollection()->findBy(['id!=' => $addedValues]) as $parameterValue) {
					$this->orm->product->removeParameterValue($this->product, $parameterValue);
				}
			}
		}

		if ($mutationCode === Mutation::DEFAULT_ISO_CODE) {
			$this->syncWarranty($productLocalization);
		}
	}

	private function syncWarranty(ProductLocalization $productLocalization): void
	{
		$warrantyB2B = (int) ($this->importCacheOdoo->data->warranty_b2b ?? 0);
		if ($warrantyB2B > 0) {
			$parameterB2B = $this->getParameter($productLocalization->mutation, Parameter::UID_WARRANTY_B2B, 'Záruka B2B', Parameter::TYPE_NUMBER, uid: Parameter::UID_WARRANTY_B2B, unit: 'měsíců');
			$parameterValueB2B = $this->getParameterValue($productLocalization->mutation, $parameterB2B, 'warranty_b2b-' . $warrantyB2B, (string) $warrantyB2B);
			$this->parameterValueModel->addValue($parameterValueB2B, $this->product);
			$this->orm->parameterValue->persistAndFlush($parameterValueB2B);
		}

		$warrantyB2C = (int) ($this->importCacheOdoo->data->warranty_b2c ?? 0);
		if ($warrantyB2C > 0) {
			$parameterB2C = $this->getParameter($productLocalization->mutation, Parameter::UID_WARRANTY_B2C, 'Záruka B2C', Parameter::TYPE_NUMBER, uid: Parameter::UID_WARRANTY_B2C, unit: 'měsíců');
			$parameterValueB2C = $this->getParameterValue($productLocalization->mutation, $parameterB2C, 'warranty_b2c-' . $warrantyB2C, (string) $warrantyB2C);
			$this->parameterValueModel->addValue($parameterValueB2C, $this->product);
			$this->orm->parameterValue->persistAndFlush($parameterValueB2C);
		}
	}

	private function syncFaqs(ProductLocalization $productLocalization, string $mutationCode): void
	{
		if (isset($this->importCacheOdoo->data->faqs)) {
			$faqs = [];
			foreach ($this->importCacheOdoo->data->faqs as $faq) {
				$faqs[] = [
					'question' => $faq->question[$mutationCode],
					'answer' => $faq->answer[$mutationCode],
				];
			}

			if ($faqs !== []) {
				$cf = $productLocalization->customFieldsJson ?? new ArrayHash();
				if ( ! isset($cf->faq)) {
					$cf->faq    = ArrayHash::from([]);
					$cf->faq[0] = new \stdClass();
				}
				if ( ! isset($cf->faq[0])) {
					$cf->faq[0] = new \stdClass();
				}

				$cf->faq[0]->items       = $faqs;
				$productLocalization->cf = $cf;
			}
		}
	}

	private function createOrUpdatePostType(SynchronizablePostTypeModel $postTypeModel, Mutation $mutation, ParameterValue $parameterValue): void
	{
		$this->translator->setMutation($mutation);
		$postTypeModel->createOrUpdate($mutation, $parameterValue->value, $parameterValue->extId, $parameterValue);
	}

}
