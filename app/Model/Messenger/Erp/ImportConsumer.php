<?php
declare(strict_types=1);

namespace App\Model\Messenger\Erp;

use App\Exceptions\LogicException;
use App\Model\Erp\Exception\ImportException;
use App\Model\InjectableInterface;
use App\Model\Messenger\Erp\Events\ImportFailed;
use App\Model\Messenger\Erp\Events\ImportSuccess;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ImportCacheOdoo\ImportCacheOdoo;
use App\Model\Orm\ImportCacheOdoo\ImportCacheOdooStatus;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Predis\Client;
use Predis\ClientInterface;
use Predis\Connection\ConnectionException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\EventDispatcher\Event;
use Tracy\Debugger;

abstract class ImportConsumer implements InjectableInterface
{

	protected Orm $orm;

	protected MutationHolder $mutationHolder;

	protected MutationsHolder $mutationsHolder;

	protected array $signals = [];

	protected ?ImportCacheOdoo $importCacheOdoo = null;

	protected ?LoggerInterface $logger = null;

	protected ?Mutation $defaultMutation;

	protected array $mutations = [];

	protected array $countries = [];

	protected array $warnings = [];

	protected EventDispatcherInterface $eventDispatcher;

	protected ?Event $event = null;

	protected Client $predisClient;

	protected function setup(): void
	{
		$this->warnings = [];
	}
	protected function setMutation(Mutation $mutation): void
	{
		$this->mutationHolder->setMutation($mutation);
		$this->orm->setMutation($mutation);
	}

	/**
	 * @phpstan-param callable(): void $callback
	 */
	protected function tryCatchQueryException(callable $callback): void
	{
		try {
			$callback();
		} catch (\Throwable $e) {
			throw $e;
		}

		/*
		try {
			$callback();
		} catch (UniqueConstraintViolationException $e) {
			throw new RecoverableMessageHandlingException($e->getMessage(), previous: $e);
		} catch (QueryException $e) {
			$lowerMessage = strtolower($e->getMessage());
			$recoverableKeywords = ['deadlock','record has changed since last read in table'];
			if (array_filter($recoverableKeywords, fn($keyword) => str_contains($lowerMessage, $keyword))) {
				throw new RecoverableMessageHandlingException($e->getMessage(), previous: $e);
			}
			throw $e;
		}*/
	}

	protected function checkMessageAndImport(ImportMessage $importMessage): void
	{
		$this->signals = $importMessage->getSignals();
		$this->warnings = [];

		try {
			$this->predisClient->ping();
		} catch (ConnectionException) {
			$this->predisClient->disconnect();
			$this->predisClient->connect();
		}

		$this->orm->reconnect();
		$this->defaultMutation = $this->mutationsHolder->getDefault();
		//$this->mutationHolder->setMutation($this->defaultMutation);
		//$this->orm->setMutation($this->defaultMutation);
		$this->setMutation($this->defaultMutation);
		$this->orm->setPublicOnly(false);

		$this->mutations = $this->orm->mutation->findBy(['isoCode!=' => null])->fetchPairs('isoCode');
		$this->countries = $this->orm->state->findBy(['extId!=' => null])->fetchPairs('extId');

		$this->setup();

		$this->importCacheOdoo = $this->initImportCacheChange($importMessage);
		if ($this->importCacheOdoo !== null) {
			try {
				$this->importCacheOdoo->status     = ImportCacheOdooStatus::Success;
				$this->importCacheOdoo->importTime = new DateTimeImmutable();
				$this->importCacheOdoo->message    = 'ok';

				$this->logger?->debug('Starting consumer \'' . static::class . '\' with id: ' . $this->importCacheOdoo->getPersistedId(), ['consumer_name' => $_ENV['MESSENGER_CONSUMER_NAME']]);

				if (isset($this->importCacheOdoo->data->removed) && $this->importCacheOdoo->data->removed) {
					$this->doRemove();
					$this->importCacheOdoo->message = 'removed';
				} else {
					$this->doImport();
				}

				if ($this->hasWarning()) {
					$this->importCacheOdoo->status  = ImportCacheOdooStatus::Warning;
					$this->importCacheOdoo->message = implode(' | ', $this->warnings);
				}
				$this->event = new ImportSuccess();
				$this->logger?->debug('Finished consume \'' . static::class . '\' with id: ' . $this->importCacheOdoo->getPersistedId(), ['consumer_name' => $_ENV['MESSENGER_CONSUMER_NAME']]);

			} catch (ImportException $e) {
				$this->handleStatusException($e);
			} catch (\Throwable $e) {
				$this->handleThrowable($e);
			}
			$this->save();
			$this->dispatchEvents();
		}
	}

	protected function handleStatusException(\Throwable $e): void
	{
		throw $e; // Send to FailedEventSubscriber
	}

	protected function handleThrowable(\Throwable $e): void
	{
		$this->importCacheOdoo->status  = ImportCacheOdooStatus::Failed;
		$this->importCacheOdoo->message = (string) $e;
		$this->importCacheOdoo->importTime = null;

		$this->logger?->error($e->getMessage(),	['exception' => $e, 'consumer_name' => $_ENV['MESSENGER_CONSUMER_NAME']]);

		$this->event = new ImportFailed();
		$this->dispatchEvents();

		// push to retry, after retry limit is reached, message will be moved to failed queue
		throw $e;
	}

	public function dispatchEvents(): void
	{
		if ($this->event !== null) {
			$this->eventDispatcher->dispatch($this->event);
		}
	}

	protected function initImportCacheChange(ImportMessage $message): ?ImportCacheOdoo
	{
		$importCacheChange = $this->orm->importCacheOdoo->getById($message->getImportCacheChangeId());
		if ($importCacheChange === null) {
			Debugger::log(new LogicException('ImportMessage not found.'), Debugger::EXCEPTION);
			return null;
		}

		assert($importCacheChange->type->createImportMessage()::class === $message::class);
		return $importCacheChange;
	}

	protected function save(): void
	{
		$this->orm->persistAndFlush($this->importCacheOdoo);
	}

	abstract protected function doImport(): void;

	abstract protected function doRemove(): void;

	protected function hasSignal(ImportMessageSignal $importMessageSignal): bool
	{
		return in_array($importMessageSignal, $this->signals);
	}

	public function injectServices(
		Orm $orm,
		MutationsHolder $mutationsHolder,
		MutationHolder $mutationHolder,
		EventDispatcherInterface $eventDispatcher,
		ClientInterface $predisClient,
	): void
	{
		$this->orm = $orm;
		$this->mutationHolder = $mutationHolder;
		$this->mutationsHolder = $mutationsHolder;
		$this->eventDispatcher = $eventDispatcher;
		assert($predisClient instanceof Client);
		$this->predisClient = $predisClient;
	}

	protected function createChecksum(ArrayHash $data): string
	{
		return md5(Json::encode($data));
	}

	protected function addWarning(string $message): void
	{
		$this->warnings[] = $message;
	}

	protected function hasWarning(): bool
	{
		return count($this->warnings) > 0;
	}

}
