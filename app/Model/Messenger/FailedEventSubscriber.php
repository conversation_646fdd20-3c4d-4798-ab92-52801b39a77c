<?php
declare(strict_types=1);

namespace App\Model\Messenger;

use App\Model\Erp\Exception\ImportException;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Erp\Exception\WarningException;
use App\Model\Messenger\Erp\ImportMessage;
use App\Model\Orm\ImportCacheOdoo\ImportCacheOdooStatus;
use App\Model\Orm\Orm;
use Contributte\Monolog\LoggerManager;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Event\WorkerMessageFailedEvent;
use Symfony\Component\Messenger\Stamp\RedeliveryStamp;
use Symfony\Component\Messenger\Stamp\SentToFailureTransportStamp;
use Tracy\Debugger;
use Tracy\ILogger;

final readonly class FailedEventSubscriber implements EventSubscriberInterface
{

	public function __construct(
		private LoggerManager $loggerManager,
		private Orm $orm,
	)
	{
	}

	private function getOriginalException(\Throwable $e): \Throwable
	{
		if ($e->getPrevious() !== null) {
			return $this->getOriginalException($e->getPrevious());
		}
		return $e;
	}

	public function updateImportCache(object $message, array $data, bool $withFlush = true): void
	{
		if ($message instanceof ImportMessage) {
			$importCacheId = $message->getImportCacheChangeId();
			$importCache   = $this->orm->importCacheOdoo->getById($importCacheId);
			if ($importCache !== null) {

				foreach ($data as $key => $value) {
					$importCache->$key = $value;
				}

				if ($withFlush) {
					$this->orm->persistAndFlush($importCache);
				}
			}
		}
	}

	public function __invoke(WorkerMessageFailedEvent $event): void
	{
		// retrying
		$message = $event->getEnvelope()->getMessage();
		$retryCount = RedeliveryStamp::getRetryCountFromEnvelope($event->getEnvelope());
		$this->updateImportCache($message, ['retryCount' => $retryCount]);

		$exception = $this->getOriginalException($event->getThrowable());

		// ImportException (SkippedException, WarningException) not sent to failed queue by adding stamp already sent
		// Trick Messenger into thinking ImportException messages were already sent to failure transport
		// This prevents them from actually being queued there - they get discarded instead
		if ($exception instanceof ImportException) {
			$event->addStamps(new SentToFailureTransportStamp($event->getReceiverName()));
		}

		if ($event->willRetry()) {
			return;
		}

		// if not retrying, message failed
		if ($exception instanceof SkippedException) {
			$this->updateImportCache($message, ['status' => ImportCacheOdooStatus::Skipped, 'message' => $exception->getMessage()]);
		} elseif ($exception instanceof WarningException) {
			$this->updateImportCache($message, ['status' => ImportCacheOdooStatus::Warning, 'message' => $exception->getMessage()]);
		} else {
			Debugger::log($exception, ILogger::EXCEPTION);
			$this->updateImportCache($message, ['status' => ImportCacheOdooStatus::Error, 'message' => (string) $exception]);
		}

		$logger = $this->loggerManager->get('messenger_failed');
		$logger->error('Failed message', ['message' => $message::class, 'exception' => $event->getThrowable()->getPrevious()?->getMessage() ?? $event->getThrowable()->getMessage(), 'e' => $this->getOriginalException($event->getThrowable())]);
	}

	public static function getSubscribedEvents(): array
	{
		return [
			WorkerMessageFailedEvent::class => [
				['__invoke', 0],
			],
		];
	}

}
