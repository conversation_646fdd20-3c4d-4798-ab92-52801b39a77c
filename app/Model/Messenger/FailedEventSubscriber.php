<?php
declare(strict_types=1);

namespace App\Model\Messenger;

use App\Model\Erp\Exception\SkippedException;
use App\Model\Erp\Exception\WarningException;
use App\Model\Messenger\Erp\ImportMessage;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\ImportCacheOdoo\ImportCacheOdooStatus;
use App\Model\Orm\Orm;
use Contributte\Monolog\LoggerManager;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Event\WorkerMessageFailedEvent;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\Stamp\RedeliveryStamp;
use Symfony\Component\Messenger\Stamp\SentToFailureTransportStamp;

final readonly class FailedEventSubscriber implements EventSubscriberInterface
{

	public function __construct(
		private LoggerManager $loggerManager,
		private Orm $orm,
	)
	{
	}

	private function getOriginalException(\Throwable $e): \Throwable
	{
		if ($e->getPrevious() !== null) {
			return $this->getOriginalException($e->getPrevious());
		}
		return $e;
	}

	public function __invoke(WorkerMessageFailedEvent $event): void
	{


		$retryCount = $event->getEnvelope()->last(RedeliveryStamp::class)?->getRetryCount() ?? 0;

		if ($retryCount >= 10) {
			file_put_contents(LOG_DIR . '/failedcount.log', date('Y-m-d H:i:s') . " Failed message ".$retryCount." - ".(int)$event->willRetry()."\n", FILE_APPEND);

			// Ukončenie retrying správy
			$event->setForRetry(false);
		}

		if ($event->willRetry()) { // Pokusa sa spracovat este raz
			return;
		}
		$exception = $this->getOriginalException($event->getThrowable());
		$message = $event->getEnvelope()->getMessage();


		$importCacheId = null;

		if ($message instanceof ImportMessage) {
			$importCacheId = $message->getImportCacheChangeId();
			$importCache = $this->orm->importCacheOdoo->getById($importCacheId);
			if ($importCache !== null) {
				if ($exception instanceof SkippedException) {
					$importCache->status  = ImportCacheOdooStatus::Skipped;
					$importCache->message = $exception->getMessage();
				} elseif ($exception instanceof WarningException) {
					$importCache->status  = ImportCacheOdooStatus::Warning;
					$importCache->message = $exception->getMessage();
				} else {
					$importCache->status = ImportCacheOdooStatus::Error;
					$importCache->message = (string) $exception;
				}
				$this->orm->persistAndFlush($importCache);
			}
		}

		$logger = $this->loggerManager->get('messenger_failed');
		$logger->error('Failed message', ['importCacheId' => $importCacheId, 'message' => $message::class, 'exception' => $event->getThrowable()->getPrevious()?->getMessage() ?? $event->getThrowable()->getMessage()]);

	}

	public static function getSubscribedEvents(): array
	{
		return [
			WorkerMessageFailedEvent::class => [
				['__invoke', 0],
			],
		];
	}
}
