<?php declare(strict_types = 1);

namespace App\Model\Orm\Alias;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\Traits\HasTreeRepository;
use App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalization;
use App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalizationRepository;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Discount\Model\Orm\DiscountLocalization\HasDiscountLocalizationRepository;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalizationRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Author\Model\Orm\HasAuthorRepository;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\HasBlogRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Glossary\Model\Orm\Localization\GlossaryLocalization;
use App\PostType\Glossary\Model\Orm\Localization\GlossaryLocalizationRepository;
use App\PostType\Faq\Model\Orm\Localization\FaqLocalization;
use App\PostType\Faq\Model\Orm\Localization\FaqLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $alias
 * @property string $module {enum self::MODULE_*}
 * @property int $referenceId
 *
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$aliases}
 *
 *
 * VIRTUALS
 * @property-read ?Tree $parent {virtual}
 * @property-read string $name {virtual}
 */
final class Alias extends Entity
{

	use HasTreeRepository;
	use HasBlogRepository;
	use HasAuthorRepository;
	use HasDiscountLocalizationRepository;

	public const MODULE_TREE = 'tree';
	public const MODULE_BLOG = 'blogLocalization';
	public const MODULE_BLOG_TAG = 'blogTagLocalization';
	public const MODULE_PRODUCT = 'productLocalization';
	public const MODULE_AUTHOR = 'authorLocalization';
	public const MODULE_SEOLINK = 'seoLinkLocalization';
	public const MODULE_DISCOUNT = 'discountLocalization';
	public const MODULE_MENU_MAIN = 'menuMainLocalization';
	public const MODULE_TAG = 'tagLocalization';
	public const MODULE_FAQ = 'faqLocalization';

	public const MODULE_GLOSSARY = 'glossaryLocalization';
	public const MODULE_BRAND = 'brandLocalization';

	private BlogLocalizationRepository $blogLocalizationRepository;

	private AuthorLocalizationRepository $authorLocalizationRepository;

	private BlogTagLocalizationRepository $blogTagLocalizationRepository;

	private ProductLocalizationRepository $productLocalizationRepository;

	private SeoLinkLocalizationRepository $seoLinkLocalizationRepository;

	private MenuMainLocalizationRepository $menuMainLocalizationRepository;
	private TagLocalizationRepository $tagLocalizationRepository;
	private FaqLocalizationRepository $faqLocalizationRepository;

	private GlossaryLocalizationRepository $glossaryLocalizationRepository;
	private BrandLocalizationRepository $brandLocalizationRepository;

	public function injectRepositories(
		BlogTagLocalizationRepository $blogTagLocalizationRepository,
		AuthorLocalizationRepository $authorLocalizationRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		ProductLocalizationRepository $productLocalizationRepository,
		SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		MenuMainLocalizationRepository $menuMainLocalizationRepository,
		TagLocalizationRepository $tagLocalizationRepository,
		FaqLocalizationRepository $faqLocalizationRepository,
		GlossaryLocalizationRepository $glossaryLocalizationRepository,
		BrandLocalizationRepository $brandLocalizationRepository,
	): void
	{
		$this->authorLocalizationRepository = $authorLocalizationRepository;
		$this->blogLocalizationRepository = $blogLocalizationRepository;
		$this->blogTagLocalizationRepository = $blogTagLocalizationRepository;
		$this->productLocalizationRepository = $productLocalizationRepository;
		$this->seoLinkLocalizationRepository = $seoLinkLocalizationRepository;
		$this->menuMainLocalizationRepository = $menuMainLocalizationRepository;
		$this->tagLocalizationRepository = $tagLocalizationRepository;
		$this->faqLocalizationRepository = $faqLocalizationRepository;
		$this->glossaryLocalizationRepository = $glossaryLocalizationRepository;
		$this->brandLocalizationRepository = $brandLocalizationRepository;
	}

	public function __toString(): string
	{
		return $this->isPersisted() ? $this->alias : '';
	}

	protected function getterParent(): Tree|BlogLocalization|AuthorLocalization|TagLocalization|BlogTagLocalization|ProductLocalization|SeoLinkLocalization|null|DiscountLocalization|MenuMainLocalization|GlossaryLocalization|FaqLocalization|BrandLocalization
	{
		$repository = $this->module . 'Repository';
		return $this->$repository->getById($this->referenceId);
	}

	protected function getterName(): string
	{
		return $this->alias;
	}

	public function getUrl(): string
	{
		return $this->mutation->getUrl() . '/' . $this->alias;
	}

}
