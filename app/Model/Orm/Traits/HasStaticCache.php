<?php

declare(strict_types=1);

namespace App\Model\Orm\Traits;

use App\Infrastructure\Latte\Functions;
use Closure;
use Nette\Utils\Json;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

trait HasStaticCache {

	protected static array $staticCache = [];

	public function flushCache(?string $key = null): void {
		if ($key === null) {
			static::$staticCache = [];
		} elseif(isset(static::$staticCache[$key])) {
			unset(static::$staticCache[$key]);
		}

	}

	/**
	 * @phpstan-param  Closure(): (mixed) $generator
	 */
	protected function loadCache(string $key, callable $generator): mixed
	{
		$data = static::$staticCache[$key] ?? null;

		if ($data === null || ! $this->validateStaticCacheData($data) && is_callable($generator)) {
			try {
				$data = $generator();
				static::$staticCache[$key] = $data;
			} catch (\Throwable $e) {
				unset(static::$staticCache[$key]);
				throw $e;
			}
		}

		return $data;
	}
	/**
	 * @phpstan-param  Closure(): (mixed) $generator
	 */
	protected function tryLoadCache(string $key, callable $generator): mixed
	{
		$data = $this->loadCache($key, $generator);

		if ($data === null || ! $this->validateStaticCacheData($data)) {
			$this->flushCache();
			$data = $this->loadCache($key, $generator);
		}

		return $data;
	}

	protected function createCacheKey(mixed ...$args): string
	{
		return Functions::cacheKey(... $args);
	}


	private function validateStaticCacheData(mixed $data): bool
	{
		if ($data === null) {
			return false;
		}

		if ($data instanceof IEntity) {
			return $data->isAttached();
		}

		if (is_array($data) && isset(array_values($data)[0]) && array_values($data)[0] instanceof IEntity) {
			return array_values($data)[0]->isAttached();
		}

		if ($data instanceof ICollection && isset($data->fetchAll()[0])) {
			return $data->fetchAll()[0]->isAttached();
		}

		return true;
	}


}
