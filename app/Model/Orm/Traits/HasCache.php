<?php

declare(strict_types=1);

namespace App\Model\Orm\Traits;

use App\Infrastructure\Latte\Functions;
use Closure;
use Nette\Utils\Json;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

trait HasCache
{

	protected array $cache = [];


	public function flushCache(?string $key = null): void
	{
		$this->cache = [];
	}


	public function onAfterPersist(): void
	{
		$this->flushCache();
	}

	protected function createCacheKey(mixed ...$args): string
	{
		return Functions::cacheKey(... $args);
	}

	/**
	 * @phpstan-param  Closure(): (mixed) $generator
	 */
	protected function loadCache(string $key, callable $generator): mixed
	{
		$data = $this->cache[$key] ?? null;

		if (! $this->validateCacheData($data) && is_callable($generator)) {
			try {
			   $data = $generator();
			   $this->cache[$key] = $data;
			} catch (\Throwable $e) {
			   unset($this->cache[$key]);
			   throw $e;
			}
		 }
	 
		 return $data;
	}

	private function validateCacheData(mixed $data): bool
	{
		if ($data === null) {
			return false;
		}

		if ($data instanceof IEntity) {
			return $data->isAttached();
		}

		if (is_array($data) && isset(array_values($data)[0]) && array_values($data)[0] instanceof IEntity) {
			return array_values($data)[0]->isAttached();
		}

		if ($data instanceof ICollection && isset($data->fetchAll()[0])) {
			return $data->fetchAll()[0]->isAttached();
		}

		return true;
	}
}
