<?php
declare(strict_types = 1);

namespace App\Model\Orm\User;

final class UserProvider {

	private ?User $userEntityCache;

	private ?bool $lastLoginState = null;


	public ?User $userEntity {
		get {
			// Ak sa zmenil stav prihlásenia, resetuj cache
			$currentLoginState = $this->userSecurity->isLoggedIn();
			if ($this->lastLoginState !== $currentLoginState) {
				$this->userEntityCache = null;
				$this->lastLoginState = $currentLoginState;
			}

			if ($this->userEntityCache === null && $this->userSecurity->id && $currentLoginState) {
				$userEntity = $this->userRepository->getById($this->userSecurity->getId());
				if ($userEntity && $userEntity->id) {
					$this->userEntityCache = $userEntity;
				}
			}
			return $this->userEntityCache;
		}
		set {
			$this->userEntityCache = $value;
		}
	}

	public function __construct(
		public \Nette\Security\User $userSecurity,
		private readonly UserRepository $userRepository,
	) {
	}
}
