<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

use stdClass;

final class RefreshResult
{

	public readonly string|null $itemName;

	public readonly int|null $amount;

	public int $availableAmount = 0;

	public stdClass $message;

	public bool $updated = false;

	public bool $removed = false;

	public bool $isGrouped = false;

	public function __construct(
		public readonly ?OrderItem $item = null
	)
	{
		$this->itemName = $this->item?->getName();
		$this->amount = $this->item?->amount;
	}


	public function addMessage(string $text, array $params = [], string $type = 'warning'): stdClass
	{
		$this->message = new stdClass();
		$this->message->text = $text;
		$this->message->type = $type;
		$this->message->params = $params;

		return $this->message;
	}

	public function addNullMessage(): stdClass
	{
		$this->message = new stdClass();
		$this->message->text = null;
		$this->message->type = null;
		$this->message->params = [];

		return $this->message;
	}


	public static function of(?OrderItem $orderItem, string $messageText, array $messageParams = [], string $messageType = 'warning'): self
	{
		$self = new self($orderItem);
		$self->addMessage($messageText, $messageParams, $messageType);

		return $self;
	}

	public static function ofNull(?OrderItem $orderItem): self
	{
		$self = new self($orderItem);
		$self->addNullMessage();

		return $self;
	}

}
