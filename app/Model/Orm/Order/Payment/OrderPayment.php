<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\RefreshResult;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use Brick\Math\BigDecimal;
use Brick\Money\Money;

/**
 * @property-read int $id {primary}
 * @property Order|null $order {1:1 Order::$payment}
 * @property PaymentMethodConfiguration|null $paymentMethod {m:1 PaymentMethodConfiguration::$orderItems}
 * @property PaymentInformation|null $information {1:1 PaymentInformation::$payment, isMain=true, cascade=[persist, remove]}
 *
 * @property string|null $paymentMethodName {default null}
 */
final class OrderPayment extends OrderItem
{

	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, PaymentMethodConfiguration $paymentMethod): OrderPayment
	{
		$item = new self();
		$item->order = $order;
		$item->paymentMethod = $paymentMethod;
		$item->paymentMethodName = $paymentMethod->name;
		$informationClass = $paymentMethod->getPaymentMethod()->getPaymentType()->getEntityClassName();
		$item->information = new $informationClass($item);
		$item->information->externalId = $item->getCurrentExternalId();
		$item->vatRate = $item->getCurrentVatRate();
		$item->vatRateValue = $item->getVatRateValue();
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->amount = $item->getMaxAvailableAmount();

		return $item;
	}

	public static function createFromErp(Order $order, PaymentMethodConfiguration $paymentMethodConfiguration, ?string $paymentMethodName/*, ?string $externalId*/, VatRate $vatRate, BigDecimal $vatRateValue, Price $unitPrice, int $amount = 1): OrderPayment
	{
		$item = new self();
		$item->order = $order;
		$item->paymentMethod = $paymentMethodConfiguration;
		$item->paymentMethodName = $paymentMethodName;
		$informationClass = $paymentMethodConfiguration->getPaymentMethod()->getPaymentType()->getEntityClassName();
		$item->information = new $informationClass($item);
		$item->vatRate = $vatRate;
		$item->vatRateValue = $vatRateValue;
		$item->unitPrice = $unitPrice;
		$item->amount = $amount;

		return $item;
	}

	public function getMaxAvailableAmount(): int
	{
		return 1;
	}

	public function getCurrentUnitPrice(): Money
	{
		$priceAmount = $this->paymentMethod->price($this->order->priceLevel, $this->order->country, $this->order);
		if ($priceAmount === null) {
			throw new \LogicException('Payment method hasnt got any prices.');
		}

		return $priceAmount;
	}

	public function getCurrentExternalId(): ?string
	{
		if (($paymentMethodPrice = $this->paymentMethod->getPrice($this->order->priceLevel, $this->order->country, $this->order->currency)) !== null) {
			return $paymentMethodPrice->externalId;
		}
		return null;
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->paymentMethod->getVatRate($this->order->country);
	}

	public function getName(): string
	{
		if ($this->paymentMethodName !== null) {
			return $this->paymentMethodName;
		}
		return $this->paymentMethod->name;
	}

	public function getDesc(): string
	{
		return $this->paymentMethod->desc;
	}

	public function refresh(bool $withRemove = true): ?RefreshResult
	{
		try {
			$expectedPrice   = $this->getCurrentUnitPrice();
			$expectedVatRate = $this->getCurrentVatRate();
		} catch (\Throwable) {
			return null;
		}

		$result = new RefreshResult($this);
		$result->availableAmount = 1;
		$result->updated = true;

		if ($this->unitPrice->asMoney()->getCurrency()->getCurrencyCode() !== $expectedPrice->getCurrency()->getCurrencyCode() || !$this->unitPrice->asMoney()->isEqualTo($expectedPrice)) {
			$result->addMessage('payment_price_changed');

			if ($this->vatRate !== $expectedVatRate) {
				$this->vatRate = $expectedVatRate;
				$result->addMessage('payment_price_and_vats_changed');
			}

			$this->unitPrice = Price::from($expectedPrice);

			return $result;
		}

		if ($this->vatRate !== $expectedVatRate) {
			$this->vatRate = $expectedVatRate;
			$result->addMessage('payment_vat_changed');
			return $result;
		}

		return null;
	}

}
