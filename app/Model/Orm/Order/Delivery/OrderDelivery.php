<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\PickupPoint\PickupPoint;
use App\Model\Orm\Order\RefreshResult;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property-read int $id {primary}
 * @property Order|null $order {1:1 Order::$delivery}
 * @property DeliveryMethodConfiguration|null $deliveryMethod {m:1 DeliveryMethodConfiguration::$orderItems}
 * @property DeliveryInformation|null $information {1:1 DeliveryInformation::$delivery, isMain=true, cascade=[persist, remove]}
 * @property string|null $deliveryMethodName {default null}
 *
 * @property DateTimeImmutable|null $dateDeliveryFrom
 * @property DateTimeImmutable|null $dateDeliveryTo
 * @property DateTimeImmutable|null $dateExpedition
 */
final class OrderDelivery extends OrderItem
{

	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, DeliveryMethodConfiguration $deliveryMethod): OrderDelivery
	{
		$item = new self();
		$item->order = $order;
		$item->deliveryMethod = $deliveryMethod;
		$item->deliveryMethodName = $deliveryMethod->name;
		$informationClass = $deliveryMethod->getDeliveryMethod()->getDeliveryType()->getEntityClassName();
		$item->information = new $informationClass($item);
		$item->information->externalId = $item->getCurrentExternalId();
		$item->vatRate = $item->getCurrentVatRate();
		$item->vatRateValue = $item->getVatRateValue();
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->amount = $item->getMaxAvailableAmount();

		$item->dateDeliveryFrom = $item->getCurrentDateDeliveryFrom();
		$item->dateDeliveryTo = $item->getCurrentDateDeliveryTo();
		$item->dateExpedition = $item->getCurrentDateDeliveryExpedition();

		return $item;
	}

	public static function createFromErp(Order $order, DeliveryMethodConfiguration $deliveryMethod, ?string $deliveryMethodName/*, ?string $externalId*/, VatRate $vatRate, BigDecimal $vatRateValue, Price $unitPrice, int $amount = 1): OrderDelivery
	{
		$item = new self();
		$item->order = $order;
		$item->deliveryMethod = $deliveryMethod;
		$item->deliveryMethodName = $deliveryMethodName;
		$informationClass = $deliveryMethod->getDeliveryMethod()->getDeliveryType()->getEntityClassName();
		$item->information = new $informationClass($item);
		$item->vatRate = $vatRate;
		$item->vatRateValue = $vatRateValue;
		$item->unitPrice = $unitPrice;
		$item->amount = $amount;

		return $item;
	}

	public function getMaxAvailableAmount(): int
	{
		return 1;
	}

	public function getCurrentUnitPrice(): Money
	{
		$priceAmount = $this->deliveryMethod->price($this->order->priceLevel, $this->order->country, $this->order);

		if ($priceAmount === null) {
			throw new \LogicException('Delivery method hasnt got any prices.');
		}
		return $priceAmount;
	}

	public function getCurrentExternalId(): ?string
	{
		if (($deliveryMethodPrice = $this->deliveryMethod->getPrice($this->order->priceLevel, $this->order->country, $this->order->currency)) !== null) {
			return $deliveryMethodPrice->externalId;
		}
		return null;
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->deliveryMethod->getVatRate($this->order->country);
	}

	protected function getCurrentDateDeliveryFrom(): ?DateTimeImmutable
	{
		return $this->deliveryMethod->getDateDeliveryFrom();
	}

	protected function getCurrentDateDeliveryTo(): ?DateTimeImmutable
	{
		return $this->deliveryMethod->getDateDeliveryTo();
	}

	protected function getCurrentDateDeliveryExpedition(): ?DateTimeImmutable
	{
		return $this->deliveryMethod->getDateDeliveryExpedition();
	}

	private function getStoredName(): string
	{
		if ($this->deliveryMethodName !== null) {
			return $this->deliveryMethodName;
		}
		return $this->deliveryMethod->name;
	}

	public function getAddress(): string
	{
		if ($this->information instanceof PickupDeliveryInformation && ($pickupPoint = $this->information->pickupPoint) instanceof PickupPoint) {
			return $pickupPoint->name;
		} else {
			return '';
		}
	}

	public function getName(): string
	{
		// if ($this->information instanceof PickupDeliveryInformation && ($pickupPoint = $this->information->pickupPoint) instanceof PickupPoint) {
		// 	$name = [$this->getStoredName()];
		// 	$name[] = $pickupPoint->name;

		// 	return implode(', ', $name);
		// }

		return $this->getStoredName();
	}

	public function getDesc(): string
	{
		return $this->deliveryMethod->desc;
	}

	public function refresh(bool $withRemove = true, bool $withUpdate = true): ?RefreshResult
	{
		try {
			$expectedPrice   = $this->getCurrentUnitPrice();
			$expectedVatRate = $this->getCurrentVatRate();
			$expectedDateDeliveryFrom = $this->getCurrentDateDeliveryFrom();
			$expectedDateDeliveryTo = $this->getCurrentDateDeliveryTo();
			$expectedDateDeliveryExpedition = $this->getCurrentDateDeliveryExpedition();
		} catch (\Throwable) {
			return null;
		}

		$result = new RefreshResult($this);
		$result->availableAmount = 1;
		$result->updated = true;

		if ($this->unitPrice->asMoney()->getCurrency()->getCurrencyCode() !== $expectedPrice->getCurrency()->getCurrencyCode() || !$this->unitPrice->asMoney()->isEqualTo($expectedPrice)) {
			//$result->addMessage('delivery_price_changed');
			$result->addNullMessage();

			if ($this->vatRate !== $expectedVatRate) {
				$result->addMessage('delivery_price_and_vats_changed');
				$this->vatRate = $expectedVatRate;
			}

			$this->unitPrice = Price::from($expectedPrice);

			return $result;
		}

		if ($this->vatRate !== $expectedVatRate) {
			$result->addMessage('delivery_vat_changed');

			$this->vatRate = $expectedVatRate;

			return $result;
		}

		if (
			$this->dateDeliveryFrom?->getTimestamp() !== $expectedDateDeliveryFrom?->getTimestamp() ||
			$this->dateDeliveryTo?->getTimestamp() !== $expectedDateDeliveryTo?->getTimestamp() ||
			$this->dateExpedition?->getTimestamp() !== $expectedDateDeliveryExpedition?->getTimestamp()
		) {
			//$result->addMessage('delivery_date_changed');
			$result->addNullMessage();

			$this->dateDeliveryFrom = $expectedDateDeliveryFrom;
			$this->dateDeliveryTo = $expectedDateDeliveryTo;
			$this->dateExpedition = $expectedDateDeliveryExpedition;

			return $result;
		}

		return null;
	}

}
