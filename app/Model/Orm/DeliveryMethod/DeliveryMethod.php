<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Product\Product;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use stdClass;

interface DeliveryMethod
{

	public function getUniqueIdentifier(): string;

	public function getDeliveryType(): DeliveryType;

	public function supportsTracking(): bool;

	public function getTrackingUrl(string $trackingCode): string;

	public function isAvailableForOrder(OrderProxy $order): bool;

	public function isAvailableForProduct(Product $product): bool;

	public function onOrderPlaced(Order $order): void;

	public function onOrderCanceled(Order $order): void;

	public function useDeliveryAddress(): bool;

	public function processDeliveryDetails(ArrayHash $details): ArrayHash;

	public function supportsPickup(): bool;

	public function getPickupConfig(DeliveryMethodConfiguration $methodConfiguration, ?Money $price = null): stdClass|ArrayHash|array;

}
