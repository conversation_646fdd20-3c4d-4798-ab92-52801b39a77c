<?php declare(strict_types = 1);

namespace App\Model\Orm\PriceLevel;

use App\Exceptions\LogicException;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserProvider;

class PriceLevelModel
{

	use HasStaticCache;


	public function __construct(
		private readonly PriceLevelRepository $repository,
		private readonly UserProvider $userProvider,
	)
	{
	}

	public function getBasePriceLevel(?User $userEntity): ?PriceLevel
	{
		return $this->getAllPriceLevelByType()[PriceLevel::TYPE_DEFAULT] ?? null;
	}

	public function getAllPriceLevel(): array
	{
		return $this->tryLoadCache($this->createCacheKey('getAllPriceLevel'), function () {
			return $this->repository->findAll()->fetchAll();
		});
	}

	public function getAllPriceLevelByType(array $onlyTypes = []): array
	{
		return $this->tryLoadCache($this->createCacheKey('getAllPriceLevelByType', $onlyTypes), function () use ($onlyTypes) {
			if ($onlyTypes !== []) {
				return $this->repository->findBy(['type' => $onlyTypes])->fetchPairs('type');
			}
			return $this->repository->findAll()->fetchPairs('type');
		});
	}

	public function getDefault(): PriceLevel
	{
		$levels = $this->getAllPriceLevelByType([PriceLevel::TYPE_DEFAULT]);
		if ($levels === []) {
			throw new LogicException('Missing default price level');
		}

		if ($this->userProvider->userEntity !== null) {
			return $this->userProvider->userEntity->priceLevel;
		}

		return reset($levels);
	}

	public function getById(int $priceLevelId): ?PriceLevel
	{
		return $this->findAll()[$priceLevelId] ?? null;
	}

	public function findAll(): array
	{
		return $this->tryLoadCache($this->createCacheKey('findAll'), function () {
			return $this->repository->findAll()->fetchPairs('id');
		});
	}

}
