{block content}
	{snippet content}
		<div class="row-main">
			{embed $templates.'/part/box/header.latte', class: 'u-mb-sm u-mb-md@md', object: $object, templates: $templates, translator: $translator}
				{block extra}
					{* TODO BE: nezávazná poptávka *}
					{php $btns = [(object) array('link' => '#', 'lang' => 'btn_inquiry', 'naja' => true)]}
					{include $templates.'/part/box/top.latte', btns: $btns, items: $object->cf->header_usp??->items ?? []}
				{/block}
			{/embed}
			{control customContentRenderer}
            {php $personCtaCf = $object->cf->person_cta ?? false}
			{embed $templates.'/part/box/person-cta.latte', person: isset($personCtaCf->person) ? $personCtaCf->person->getEntitY() ?? false : false, content: $personCtaCf->content ?? false}
				{block content}
					<p class="u-ta-c">
						{* TODO BE: modal form *}
						<a href="#" class="btn btn--secondary" data-naja-history="off" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content">
							<span class="btn__text">
								<span class="btn__inner">
									{_"btn_inquiry"}
								</span>
							</span>
						</a>
					</p>
				{/block}
			{/embed}

            {include $templates.'/part/crossroad/dummy-articles-carousel.latte', gradient: true, title: $translator->translate('title_articles_recommended')}
			<hr class="u-mb-sm u-mb-md@md">
			{include $templates.'/part/box/benefits.latte'}
		</div>
	{/snippet}
{/block}
