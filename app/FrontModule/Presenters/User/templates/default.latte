{block content}
{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\User\User $userEntity}

<div class="row-main">
	{snippet content}
		<div class="u-pt-sm u-pt-md@md u-mb-md u-mb-2xl@md">
			<div class="grid grid--x-sm grid--y-sm">
				<div class="grid__cell size--3-12@xl u-d-n u-d-b@xl">
					{control userSideMenu}
				</div>
				<div class="grid__cell size--9-12@xl u-mb-last-0">
					{control breadcrumb}

					{if !empty($registrationCompleteMsg)}
						{include $templates . '/part/box/content.latte', content: $registrationCompleteMsg}
					{/if}

					{* Nadpis *}
					<div class="u-mb-last-0 tw-mb-[2.4rem] md:tw-mb-[3.2rem]">
						{if $object->uid == 'userSection'}
							{* Nadpis pro dashboard *}
							{var $firstNameVocativeReplace = $firstNameVocative ? ', ' . $firstNameVocative : ''}
							<h1 class="tw-mb-[1.2rem]">{(trim($translator->translate('title_user_profile'))|replace:'%vocativ', $firstNameVocativeReplace)}</h1>
						{elseif $object->uid === 'userOrderHistory' && isset($order)}
							{* Nadpis pro detail objednávky *}
							<div class="tw-flex tw-mb-[1.2rem] tw-gap-[0.8rem] md:tw-gap-[1.2rem] tw-items-center">
								<h1 class="tw-mb-0">{_'order_detail_title'|replace:'%i',$order->orderNumber}</h1>
								<p class="tw-mb-0 tw-text-help tw-text-[1.2rem] md:tw-text-[1.3rem]">
									{_'order_status'}: {include $templates.'/part/core/status.latte', order: $order}
								</p>
							</div>
						{else}
							{* Standardní nadpis *}
							<h1 class="tw-mb-[1.2rem]">{$object->nameHeading ? $object->nameHeading : $object->name}</h1>
						{/if}

						{* Obecná upozornění - TODO BE *}
						{* <p n:if="in_array($object->uid, ['userSection', 'userOrderHistory'])" class="message message--md message--error tw-mb-[0.8rem] md:tw-mb-[1.2rem]">
							<span class="message__emoji">🚨</span>
							<span class="message__content">
								Věnujte, prosím, pozornost seznamu závazků překračujících dobu splatnosti. <b><a href="#">Objednávka č. #123456789</a> čeká na zaplacení.</b>
							</span>
						</p> *}
					</div>
					<p n:if="$object->annotation ?? false" class="tw-mb-[1.2rem]">{$object->annotation|texy|noescape}</p>

					{* Obsah dle uid *}
					{if $object->uid == 'userSection'}
						{* Přehled *}

						{* Moje kurzy *}
						{var $allClasses = $userEntity->getAllClasses()}
						{include $templates.'/part/crossroad/table-courses.latte', title: $translator->translate('title_my_courses'), items: $allClasses, emptyMsg: isset($pages->courses) ? ($translator->translate('empty_my_courses')|replace:'%link', $presenter->link($pages->courses)) : false}

						{* Historie objednávek *}
						{var $maxVisibleProducts = 6}

						{var $orders = $userEntity->orders->toCollection()->findBy(['state!=' => [App\Model\Orm\Order\OrderState::Draft, App\Model\Orm\Order\OrderState::SavedForLater]])->limitBy($maxVisibleProducts)->orderBy('placedAt', Nextras\Orm\Collection\ICollection::DESC)}
						{include $templates.'/part/crossroad/table-orders.latte', title: $translator->translate('title_user_overview_orders'), showMore: true, maxVisibleProducts: $maxVisibleProducts, orders: $orders, emptyMsg: $translator->translate('empty_orders')}

						{* Naposledy prohlížené *}
						{control lastVisitedProducts}

					{elseif $object->uid == 'userCourses'}
						{var $plannedClasses = $userEntity->getPlannedClasses()}
						{var $pastClasses = $userEntity->getPastClasses()}
						{* Moje kurzy - if empty planed and past courses -> empty message *}
						{if count($plannedClasses) === 0 && count($pastClasses) === 0}
						{include $templates.'/part/crossroad/table-courses.latte', title: $translator->translate('title_my_courses'), items: [], emptyMsg: isset($pages->courses) ? ($translator->translate('empty_my_courses')|replace:'%link', $presenter->link($pages->courses)) : false}
						{/if}

						{* Plánované kurzy *}
						{include $templates.'/part/crossroad/table-courses.latte', title: $translator->translate('title_planned_courses'), items: $plannedClasses}

						{* Proběhlé kurzy *}
						{include $templates.'/part/crossroad/table-past-courses.latte', title: $translator->translate('title_past_courses'), items: $pastClasses}

						{* Formulář - hodnocení kurzů - TODO BE *}
						{*include $templates.'/part/form/course-quality.latte'*}

					{elseif $object->uid == 'userClaims'}
						{include $templates.'/part/box/claims-info.latte'}
						<p>
							TODO: table-claims
						</p>
						<p>
							TODO: #claimsForm
						</p>

					{elseif $object->uid == 'myReviews'}
						{* Moje hodnocení *}
						{if count($reviews) || count($productsToReview)}
							{include $templates.'/part/crossroad/user-reviews.latte', reviews=>$productsToReview, title=>'title_user_reviews_not_rated'}
							{include $templates.'/part/crossroad/user-reviews.latte', reviews=>$reviews, title=>'title_user_reviews_rated', type=>'rated'}
						{else}
							<p class="message message--md u-ta-c u-mb-0">
								{_message_empty_reviews}
							</p>
						{/if}

					{elseif $object->uid == 'userAddress'}
						{* Údaje a adresy *}
						{control profileForm}
						{control userAddressForm}
						{control newsletterProfileForm}

					{elseif $object->uid == 'userChangePassword'}
						{control changePasswordForm}
					{elseif $object->uid === 'loyaltyProgram'}
						{include 'club.latte'}
					{elseif $object->uid == 'userOrderHistory'}
						{* Detail objednávky *}
						<div n:if="isset($order)" class="u-mb-last-0">
							{control orderHistory:detail, $presenter->getParameter('orderHash')}
						</div>

						{* Historie objednávek *}
						<div n:snippet="orderHistoryControl" n:if="!isset($order)" class="u-mb-last-0">
							{control orderHistory}
						</div>
					{elseif $object->uid === 'myWatchdogs'}
						{control myWatchdog}
					{/if}
				</div>
			</div>
		</div>
	{/snippet}
	<hr class="u-mb-sm u-mb-lg@md">
	{* {include $templates.'/part/box/services.latte', showHighlighted: true} *}
	{include $templates.'/part/box/benefits.latte'}
</div>



