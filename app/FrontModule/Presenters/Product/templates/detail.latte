{block content}
	{varType App\Model\Orm\Product\Product $product}
	{varType App\Model\Orm\ProductVariant\ProductVariant $variant}
	{varType App\Model\Orm\ProductLocalization\ProductLocalization $productLocalization}

	{var $priceVat = $variant->priceVat($mutation, $priceLevel, $state)}

	{control structuredData}

	<div class="row-main">
		{snippetArea productDetail}
			{include $templates.'/part/box/product-detail.latte'}
		{/snippetArea}

		{* TODO: varianty (+ podmínka pro nevypisování kontaktní osoby v product-gallery - je součástí této kompoenenty) *}
		{* {include $templates.'/part/crossroad/variants.latte'} *}

		{* TODO: alternativy *}
		{php $control['productListOthersReading']->setTemplateParameters(['id' => 'alternatives', 'class' => 'section--bd u-mb-md u-mb-2xl@md'])}
		{control productListOthersReading}

		{* Sekce *}
		<div class="b-sections" data-controller="sections" n:snippetArea="productSections" data-header-unpin>
			{var $eventsCount = $product->classEventsPublic->countStored()}
			{* Sections setup *}
			{php $sections = [
				['title' => 'section_parameters', 'id' => 'parameters', 'showProductName' => true],

				['title' => $product->isCourse() ? 'section_course_description' : 'section_description', 'titleShort' => $product->isCourse() ? 'section_course_description_short' : 'section_description_short', 'id' => 'description', 'showProductName' => true],
				['title' => 'section_course_schedule', 'id' => 'schedule'],
				['title' => 'section_course_lecturer', 'id' => 'lecturer', 'condition' => $product->getLectors() !== []],
				['title' => 'section_course_dates', 'titleShort' => 'section_course_dates_short', 'id' => 'dates', 'forceOpen' => true, 'showProductName' => true, 'flag' => ['text' => $eventsCount, 'class' => 'flag--green']],
				['title' => 'section_course_faq', 'titleShort' => 'section_course_faq_short', 'id' => 'cource_faq', 'condition' => $productLocalization->hasFaqItems(),'hideTitleOnDesktop' => true, 'showProductName' => true],
				['title' => 'section_course_followup', 'id' => 'followup'],

				['title' => 'section_product_faq', 'titleShort' => 'section_product_faq_short', 'id' => 'product_faq', 'condition' => $productLocalization->hasFaqItems(), 'hideTitleOnDesktop' => true, 'showProductName' => true],
				['title' => 'section_rating', 'id' => 'rating', 'condition' => !$product->isCertificate, 'flag' => ['text' => $product->reviewInfo['percent'] . ' %', 'class' => 'flag--bd flag--gray-light', 'icon' => 'star', 'iconPosition' => 'after', 'showInTitleOnDesktop' => true]],
				['title' => 'section_articles', 'id' => 'articles', 'flag' => ['text' => '4', 'class' => 'flag--bd flag--gray-light', 'showInTitleOnDesktop' => true]],
				['title' => 'section_manuals_to_download', 'titleShort' => 'section_manuals_to_download_short', 'id' => 'manuals', 'condition' => $productLocalization->hasManualsToDownload(), 'showProductName' => true],
				['title' => 'section_complectation', 'id' => 'complectation', 'condition' => $productLocalization->hasPackageContents()],
				['title' => 'section_review', 'id' => 'review', 'condition' => $product->cf->specialistReview ?? false, 'titleShort' => 'section_review_short', 'showProductName' => true, 'flag' => ['text' => 'TESTED', 'class' => 'flag--blue flag--sm', 'icon' => 'check', 'showInTitleOnDesktop' => true]],
			]}

			{* Skrytí nerelevantních sekcí *}
			{foreach $sections as $key => $section}
				{if $product->isOnlineCourse() && in_array($section['id'],['dates', 'complectation', 'review'])}
					{* Online kurz *}
					{php $sections[$key] = null}
				{elseif $product->isCourse() && in_array($section['id'],['product_faq', 'complectation', 'review'])}
					{* Kurz *}
					{php $sections[$key] = null}
				{elseif !$product->isCourse() && in_array($section['id'],['cource_faq', 'dates', 'schedule', 'lecturer'])}
					{* Produkt *}
					{php $sections[$key] = null}
				{elseif in_array($section['id'], ['rating', 'followup', 'articles'])}{* TODO vyjmenované sekce zatím sktýt *}
					{* Dočasné skrytí *}
					{php $sections[$key] = null}
				{/if}
			{/foreach}
			{include $templates.'/part/menu/product.latte', class: 'b-sections__menu', sections: $sections}

			<div class="b-sections__main">

				{foreach $sections as $section}
					{continueIf $section === null}

					{* Special content in section header *}
					{switch $section['id']}
						{case 'parameters'}
							{capture $extra}
								{* TODO BE *}
								<ul class="u-reset-ul tw-flex tw-gap-[3.2rem] tw-text-[1.3rem] tw-flex-[0_0_auto] tw-overflow-hidden max-xxl:tw-hidden">
									<li class="tw-relative before:tw-content-[''] before:tw-absolute before:tw-right-[-1.6rem] before:tw-top-0 before:tw-bottom-0 before:tw-w-[0.1rem] before:tw-bg-bd">
										Parametr: hodnota
									</li>
								</ul>
							{/capture}
						{default }
							{php $extra = false}
					{/switch}

					{embed $templates.'/part/box/section.latte', object: $object, extra: $extra ?? false, id: $section['id'], condition: $section['condition'] ?? true, title: $section['title'], titleShort: $section['titleShort'] ?? false, showProductName: $section['showProductName'] ?? false, flag: $section['flag'] ?? false, forceOpen: $section['forceOpen'] ?? false, hideTitleOnDesktop: $section['hideTitleOnDesktop'] ?? false}
						{block content}
							{switch $section['id']}
								{case 'description'}
									{include $templates.'/part/box/content.latte', class: 'b-sections__content'}
								{case 'schedule'}
									{include $templates.'/part/box/duration.latte'}
									{include $templates.'/part/box/schedule.latte'}
								{case 'lecturer'}
									{include $templates.'/part/crossroad/course-lecturers.latte'}
								{case 'dates'}
									{include $templates.'/part/box/dates.latte'}

									<p n:if="$productLocalization->hasRequalificationPossibility($state)" class="message message--lg message--warning u-mb-sm">
										<span class="message__emoji">ℹ️</span>
										<span class="message__content">{_"section_course_dates_info"|noescape}</span>
									</p>

									{if false} {* TODO napojit *}
										<p n:if="$eventsCount" class="message message--lg message--warning u-mb-sm">
											<span class="message__emoji">👌</span>
											<span class="message__content">{_"section_course_dates_tip"|noescape}</span>
										</p>

										{include $templates.'/part/box/course-special.latte'} {* TODO napojit *}
										<p class="u-ta-c u-mb-0"><a href="#">{_"btn_voucher_pick_date"}</a></p> {* TODO napojit *}
									{/if}
								{case 'product_faq', 'cource_faq'}
									{capture $faqTitle}{_$section['title']} <span class="u-c-help u-d-b@xl">{$object->name}</span>{/capture}
									{control faqProductDetail, [
										title: $faqTitle,
										limitWidth: false,
										titleClass: 'u-d-n u-d-b@lg',
									]}
								{case 'rating'}
									{include $templates.'/part/box/reviews-total.latte'}
									{include $templates.'/part/crossroad/reviews.latte'}
								{case 'followup'}
									{include $templates.'/part/crossroad/followup.latte'} {* TODO napojit *}
								{case 'articles'}
									{include $templates.'/part/crossroad/dummy-articles-carousel.latte'} {* TODO napojit *}
								{case 'manuals'}
									{control manualsToDownload}
								{case 'complectation'}
									{control productComplectation}
								{case 'review'}
									{control specialistReview}
								{case 'parameters'}
									{control productParameters}
							{/switch}
						{/block}
					{/embed}
				{/foreach}
			</div>
		</div>

		{* {include $templates.'/part/box/services.latte', showHighlighted: true} *}
		{include $templates.'/part/box/benefits.latte'}
	</div>
{/block}

{* <p class="u-mb-0">
	<a href="{plink $pages->productReviewAdd, productId => $product->id}" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--productReviewAdd">
		{if $userEntity?->reviews->toCollection()->getBy(['product' => $product])}
			{_"btn_edit_review"}
		{else}
			{_"btn_add_review"}
		{/if}
	</a>
</p> *}

{* Série *}
{* {if $product->hasSeries()}
	{snippet productDetailListInSeries}
		{foreach $product->series as $series}
			{var $itemId = (string) $series->id}
			{php $control["productListInSeries-$itemId"]->setTemplateParameters(['class' => 'section--series section--w-line section--products u-mb-sm u-mb-lg@md'])}
			{control "productListInSeries-$itemId"}
			<hr class="u-mb-sm u-d-n@md">
		{/foreach}
	{/snippet}
{/if} *}

{* Ostatí také čtou *}
{* {if $marketingConsent}
	{snippet productDetailListOtherReading}
		{php $control['productListOthersReading']->setTemplateParameters(['id' => 'reading'])}
		{control productListOthersReading}
	{/snippet}
{/if} *}

{* {dump $product->productType} *}

{* Parametry *}
{* {embed $templates.'/part/box/product-section.latte', id=>'parameters', titleLang=>'product_section_title_parameteres', showParameters:$showParameters}
	{block content}
		<div class="grid grid--y-xs">
			<div n:ifcontent class="grid__cell size--7-12@xl">
				{cache cacheKey('productParameters', $product), expire: '24 hour', tags: $product->getTemplateCacheTags()}
					{control productParameters}
				{/cache}
			</div>
			<div class="grid__cell size--5-12@xl">
				{include $templates.'/part/menu/product-categories.latte'}
			</div>
		</div>
	{/block}
{/embed} *}

{* Hodnocení *}
{* {if !$product->isCertificate}
	{embed $templates.'/part/box/product-section.latte', id=>'rating', titleLang=>'product_section_title_rating'}
		{block contentSide}
			<p class="u-mb-0">
				<a href="{plink $pages->productReviewAdd, productId => $product->id}" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--productReviewAdd">
					{if $userEntity?->reviews->toCollection()->getBy(['product' => $product])}
						{_"btn_edit_review"}
					{else}
						{_"btn_add_review"}
					{/if}
				</a>
			</p>
		{/block}
		{block content}
			{include $templates.'/part/box/reviews-total.latte', class=>'u-maw-5-12 u-mb-xs u-mb-sm@lg'}
			{include $templates.'/part/crossroad/reviews.latte', class=>'u-maw-8-12'}
		{/block}
	{/embed}
{/if} *}

{* Mohlo by vás zajímat *}
{* {if $marketingConsent}
	{snippet productDetailListInterested}
		{if !count($presenter->lastVisitedProduct->getProductIds()) == 0}
			{php $control['productListInterested']->setTemplateParameters(['class' => 'section--products section--w-line'])}
		{/if}
		{control productListInterested}
	{/snippet}
{/if} *}
