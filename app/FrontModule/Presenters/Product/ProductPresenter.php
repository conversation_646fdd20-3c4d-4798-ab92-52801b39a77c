<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Product;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Event\ProductClicked;
use App\Event\ProductDetailView;
use App\Event\Provider\ProductClickedProvider;
use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\AddToCart\AddToCartFactory;
use App\FrontModule\Components\AddToCompareComponent\AddToCompareComponent;
use App\FrontModule\Components\AddToCompareComponent\AddToCompareComponentFactory;
use App\FrontModule\Components\AddToMyLibrary\AddToMyLibrary;
use App\FrontModule\Components\AddToMyLibrary\AddToMyLibraryFactory;
use App\FrontModule\Components\AlternativeProducts\AlternativeProducts;
use App\FrontModule\Components\AlternativeProducts\AlternativeProductsFactory;
use App\FrontModule\Components\DeliveryOptions\DeliveryOptions;
use App\FrontModule\Components\DeliveryOptions\DeliveryOptionsFactory;
use App\FrontModule\Components\ManualsToDownload\ManualsToDownloadComponent;
use App\FrontModule\Components\ManualsToDownload\ManualsToDownloadComponentFactory;
use App\FrontModule\Components\FaqProductDetail\FaqProductDetailComponent;
use App\FrontModule\Components\FaqProductDetail\FaqProductDetailComponentFactory;
use App\FrontModule\Components\ProductComplectation\ProductComplectationComponent;
use App\FrontModule\Components\ProductComplectation\ProductComplectationComponentFactory;
use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Components\ProductParameters\ProductParameters;
use App\FrontModule\Components\ProductParameters\ProductParametersFactory;
use App\FrontModule\Components\SpecialistReview\SpecialistReview;
use App\FrontModule\Components\SpecialistReview\SpecialistReviewFactory;
use App\FrontModule\Components\StructuredData\Facade\Product\StructuredDataProductDefaultFacade;
use App\FrontModule\Components\StructuredData\Facade\Product\StructuredDataProductFacade;
use App\FrontModule\Components\StructuredData\StructuredData;
use App\FrontModule\Components\StructuredData\StructuredDataFactory;
use App\FrontModule\Components\VariantPicker\VariantPicker;
use App\FrontModule\Components\VariantPicker\VariantPickerFactory;
use App\FrontModule\Components\Watchdog\Watchdog;
use App\FrontModule\Components\Watchdog\WatchdogFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Infrastructure\Latte\Filters;
use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Order\OrderRepository;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\ProductReview\FrontModule\Components\ProductDetailReviews\ProductDetailReviews;
use App\PostType\ProductReview\FrontModule\Components\ProductDetailReviews\ProductDetailReviewsFactory;
use App\PostType\ProductReview\FrontModule\Components\ProductReviewAdd\ProductReviewAdd;
use App\PostType\ProductReview\FrontModule\Components\ProductReviewAdd\ProductReviewAddFactory;
use App\PostType\ProductReview\FrontModule\Components\ProductReviews\ProductReviews;
use App\PostType\ProductReview\FrontModule\Components\ProductReviews\ProductReviewsFactory;
use Nette\Application\BadRequestException;
use App\PostType\Tag\Model\Checker\Checker;
use App\PostType\Tag\Model\Checker\CheckerDTO;
use App\PostType\Tag\Model\TagType;
use Nette\Application\UI\Multiplier;
use Nette\DI\Attributes\Inject;

/**
 * @method ProductVariant getObject()
 */
class ProductPresenter extends BasePresenter
{

	protected ?ProductVariant $variant;

	private Product $product;

	private ProductLocalization $productLocalization;

	#[Inject]
	public ProductModel $productModel;
	#[Inject]
	public AddToMyLibraryFactory $addToMyLibraryFactory;
	#[Inject]
	public AddToCartFactory $addToCartFactory;


	#[Inject]
	public ProductReviewAddFactory $productReviewAddFactory;
	#[Inject]
	public VariantPickerFactory $variantPickerFactory;

	#[Inject]
	public ProductDetailReviewsFactory $productDetailReviewsFactory;

	#[Inject]
	public ProductReviewsFactory $productReviewsFactory;

	#[Inject]
	public DeliveryOptionsFactory $deliveryOptionsFactory;

	#[Inject]
	public DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository;

	#[Inject]
	public StructuredDataFactory $structuredDataFactory;

	#[Inject]
	public ProductClickedProvider $productClickedProvider;
	#[Inject]
	public WatchdogFactory $watchdogFactory;

	#[Inject]
	public ProductComplectationComponentFactory $productComplectationComponentFactory;

	#[Inject]
	public ManualsToDownloadComponentFactory $manualsToDownloadComponentFactory;

	#[Inject]
	public FaqProductDetailComponentFactory $faqProductDetailComponentFactory;

	#[Inject]
	public SpecialistReviewFactory $specialistReviewFactory;

	#[Inject]
	public AlternativeProductsFactory $alternativeProductsFactory;

	#[Inject]
	public AddToCompareComponentFactory $addToCompareComponentFactory;

	public function __construct(
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly ProductParametersFactory $productParametersFactory,
		private readonly OrderRepository $orderRepository,
		private readonly Checker $tagChecker,
	)
	{
		parent::__construct();
	}

	public function actionDetail(ProductLocalization $object, mixed $v = null): void
	{
		$variantId = $v;
		$this->productLocalization = $object;
		$this->product = $this->productLocalization->product;

		if ($variantId !== null) {
			$this->variant = $object->getActiveVariantByMutation($this->mutation, $variantId);
			if ($this->variant !== null && $this->variant->product->id !== $this->product->id) {
				$this->redirect($this->productLocalization);
			}
		} else {
			$this->variant = $object->getFirstActiveVariantByMutation($this->mutation);
		}

		if ($this->variant === null && (int) $this->getParameter('show') === 1) {
			$this->variant = $this->product->firstVariant;
		}

		if ($this->variant === null) {
			throw new BadRequestException('Variant not found or not active.');
		}

		if ($this->userEntity !== null) {
			$this->getTemplate()->clubBonusTexts = $this->userModel->createClubBonusTexts($this->userEntity);
		}

		$this->setObject($this->productLocalization);
	}

	public function actionDeliveryOptions(int|null $productLocalizationId = null): void
	{
		$productLocalization = $this->orm->productLocalization->getById($productLocalizationId);

		assert($productLocalization instanceof ProductLocalization);

		$this->setObject($productLocalization);
	}

	public function renderDeliveryOptions(): void
	{
		if ($this->isAjax()) {
			$this->redrawControl('content');
		}
	}

	public function actionGallery(int $productLocalizationId): void
	{
		$productLocalization = $this->orm->productLocalization->getById($productLocalizationId);

		assert($productLocalization instanceof ProductLocalization);

		$this->setObject($productLocalization);
		$this->template->productLocalization = $productLocalization;
		$this->template->product = $productLocalization->product;
		$this->template->productEntity = $productLocalization->product;
		$this->template->images = $productLocalization->product->cf->productImagePages?->image ?? [];
	}

	public function renderGallery(int $productLocalizationId): void
	{
		if ($this->isAjax()) {
			$this->redrawControl('productPreview');
		}
	}

	public function createComponentDeliveryOptions(): DeliveryOptions
	{
		// @phpstan-ignore-next-line
		return $this->deliveryOptionsFactory->create($this->getObject()->getFirstActiveVariantByMutation($this->mutation), $this->mutation, $this->currentState, $this->priceLevel, $this->mutation->currency, $this->userProvider);
	}

	public function renderDetail(): void
	{
		if ($this->configService->getParam('shop', 'enableLastVisited')) {
			$this->lastVisitedProduct->add($this->product);
		}
		// sluzby
		$this->getTemplate()->parametersMain = $this->productModel->filterOutMainParameters($this->product);
//		bdump($this->getTemplate()->parametersMain);
		$this->getTemplate()->textMore = $this->product->mainCategory->cf->categoryOtherSettings->moreAboutBook ?? null;
		$this->template->specialServices = [];
		$this->template->product = $this->product;
		$this->template->productEntity = $this->product;
		$this->template->productDto = $this->productDtoProvider->get($this->product, $this->variant);
		$this->template->productLocalization = $this->productLocalization;
		$this->template->variant = $this->variant;
		$this->getTemplate()->hasLastMinute = false;

		if ($this->product->isCourse()) {
			$this->getTemplate()->hasLastMinute = $this->tagChecker->getCheckerFunction(TagType::lastMinute, $this->setup)(
				new CheckerDTO(productLocalization: $this->product->getLocalization($this->setup->mutation))
			);
			$this->template->hasBuyableRequalification = $this->productLocalization->hasBuyableRequalificationPossibility($this->currentState);
			$this->template->hasRequalification = $this->productLocalization->hasRequalificationPossibility($this->currentState);
		}

//		$this->getTemplate()->priceInfo = $this->product->getPriceInfo($this->setup->mutation, $this->setup->priceLevel, $this->setup->state);
		$this->getTemplate()->price = $this->product->price($this->setup->mutation, $this->setup->priceLevel, $this->setup->state);
		$this->getTemplate()->damageLevel = $this->getObject()->product->damageLevel;
		$this->getTemplate()->transitFreeLowestLPrice = $this->orm->deliveryMethod->getTransitFreeFromLevel($this->mutation, $this->currentState, $this->priceLevel, CurrencyHelper::getCurrency());
		$this->getTemplate()->currencySymbol = Filters::formatCurrency(CurrencyHelper::getCurrency());
		$this->getTemplate()->showParameters = $this->showParameters();

		$listId = null;
		$listName = null;

		if ((int) $this->productClickedProvider->id === $this->product->id) {
			$listId = $this->productClickedProvider->listId;
			$listName = $this->productClickedProvider->listName;
			$this->eventDispatcher->dispatch(
				new ProductClicked(
					productLocalization: $this->productLocalization,
					productVariant: $this->variant,
					mutation: $this->mutation,
					state: $this->currentState,
					priceLevel: $this->priceLevel,
					currency: CurrencyHelper::getCurrency(),
					listId: $listId,
					listName: $listName,
				)
			);
		}

		$this->eventDispatcher->dispatch(
			new ProductDetailView(
				productLocalization: $this->productLocalization,
				productVariant: $this->variant,
				mutation: $this->mutation,
				state: $this->currentState,
				priceLevel: $this->priceLevel,
				currency: CurrencyHelper::getCurrency(),
				listId: $listId,
				listName: $listName,
			)
		);
	}

	private function showParameters(): bool
	{
		if (!isset($this->product->mainCategory->cf)) {
			return true;
		}

		$categoryCf = $this->product->mainCategory->cf;

		if (!isset($categoryCf->categoryParametersForProductDetail->visible)) {
			return true;
		}

		return !$categoryCf->categoryParametersForProductDetail->visible;
	}

	public function actionPreorder(int $id): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(false);
		}
	}

	protected function createComponentStructuredData(): StructuredData
	{
		$facade = match ($this->getObject()->product->productType?->uid) {
			default => StructuredDataProductFacade::instance()
		};

		assert($facade instanceof StructuredDataProductDefaultFacade);
		$facade->injectDependency(
			$this->orm->parameterValue,
			fn(Product $product, ProductVariant $variant) => $this->productDtoProvider->get($product, $variant),
		);

		return $this->structuredDataFactory->create(
			$facade->setLinkFactory($this->linkFactory)
				->setOrm($this->orm)
				->setData($this->getObject())
		);
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentProductParameters(): ProductParameters
	{
		return $this->productParametersFactory->create($this->product, $this->variant, $this->mutation);
	}

	protected function createComponentProductReviews(): ProductReviews
	{
		return $this->productReviewsFactory->create($this->product, $this->mutation);
	}

	/**
	 * @return Multiplier<ProductReviewAdd>
	 */
	protected function createComponentMultiReview(): Multiplier
	{
		return new Multiplier(function ($id) {
			return $this->productReviewAddFactory->create($this->orm->product->getById($id), $this->mutation, $this->userEntity, userView: 'requestOrder', order: $this->orderRepository->getByUsersProduct($this->product, $this->user));
		});
	}

	protected function createComponentProductDetailReviews(): ProductDetailReviews
	{
		return $this->productDetailReviewsFactory->create($this->product, $this->mutation, $this->userEntity);
	}

	protected function createComponentProductListOthersReading(): ProductList
	{
		$productIds = $orderedIds = $cartIds = [];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$orderedIds = $this->userModel->findOrderedProductIds($this->user);
			$cartIds = $this->shoppingCart->getProductsIds();
			$productIds = $this->userModel->findRelatedOrderedProducts(
				productIds: [$this->getObject()->product->id],
				orderFrom: (new \DateTimeImmutable())->modify('-1 year')
			);
		}

		$productListFactory = $this->productListFactory->create($this->currentState, $this->priceLevel, $this->product->isCertificate ? ProductList::TYPE_CERTIFICATES : ProductList::TYPE_INTERESTED, $this->userEntity)
			->setExcludeProductIds([$this->getObject()->product->id])
			->setOrderedProductIds($orderedIds)
			->setCartProductIds($cartIds)
			->setLimit(21)
			->setAppendBestselling()
			->setAppendBestsellingWithExclude()
			->setTitle($this->product->isCertificate ? 'certificates_others' : 'productList_others_reading_' . $this->product->productType?->uid)
			->addOnBestsellingElasticItemList(function (array &$items): void {
				if (($category = $this->getObject()->product->mainCategory) !== null) {
					$items[] = new IsInPath($category);
				}
			});

		if (!$this->product->isCertificate) {
			$productListFactory->setIncludeProductIds(array_keys($productIds));
		}

		return $productListFactory;
	}

	public function createComponentProductListInterested() : ProductList
	{
		return $this->productListInterestedFactory->create($this->currentState, $this->priceLevel, $this->userEntity);
	}

	protected function createComponentAddToCart(): AddToCart
	{

		$productDto = $this->productDtoProvider->get($this->product, $this->variant);

		return $this->addToCartFactory->create(
			product: $this->product,
			productDto: $productDto,
			currentState: $this->currentState,
			priceLevel: $this->priceLevel,
			productVariant: $this->variant,
			type: AddToCart::TYPE_DETAIL,
		);
	}

	/**
	 * @return Multiplier<AddToCart>
	 */
	protected function createComponentAddToCartClassEvent(): Multiplier
	{
		return new Multiplier(function ($id) {
			$classEvent = $this->orm->classEvent->getById($id);
			$product = $classEvent->product;
			$variant = $product->firstVariant;

			$productDto = $this->productDtoProvider->get($product, $variant);

			return $this->addToCartFactory->create(
				product: $product,
				productDto: $productDto,
				currentState: $this->currentState,
				priceLevel: $this->priceLevel,
				productVariant: $product->firstVariant,
				classEvent: $classEvent,
				type: AddToCart::TYPE_DETAIL,
			);
		});
	}

	/**
	 * @return Multiplier<AddToCart>
	 */
	protected function createComponentAddToCartClassEventRequalification(): Multiplier
	{
		$requalificationPriceLevel = $this->orm->priceLevel->getBy(['type' => PriceLevel::TYPE_REQUALIFICATION]);
		return new Multiplier(function ($eventId) use ($requalificationPriceLevel) {
			$classEvent = $this->orm->classEvent->getById($eventId);
			$product = $classEvent->product;
			$variant = $product->firstVariant;
			$productDto = $this->productDtoProvider->get($product, $variant);

			return $this->addToCartFactory->create(
				product: $product,
				productDto: $productDto,
				currentState: $this->currentState,
				priceLevel: $requalificationPriceLevel,
				productVariant: $product->firstVariant,
				classEvent: $classEvent,
				type: AddToCart::TYPE_DETAIL,
			);
		});
	}

	public function createComponentAddToMyLibrary(): AddToMyLibrary
	{
		return $this->addToMyLibraryFactory->create($this->product, $this->userEntity, $this->mutation, $this->priceLevel, $this->currentState, CurrencyHelper::getCurrency());
	}

	public function actionWatchdog(int|null $productId = null): void
	{
		$product = $this->orm->product->getById($productId);
		$productLocalization = $product->getLocalization($this->mutation);

		assert($productLocalization instanceof ProductLocalization);

		$this->setObject($productLocalization);
	}

	public function renderWatchdog(): void
	{
		if ($this->isAjax()) {
			$this->redrawControl('content');
		}
	}

	public function createComponentWatchdog(): Watchdog
	{
		assert($this->object instanceof ProductLocalization);
		return $this->watchdogFactory->create($this->object);
	}

	public function createComponentProductComplectation(): ProductComplectationComponent
	{
		assert($this->object instanceof ProductLocalization);
		return $this->productComplectationComponentFactory->create($this->object);
	}

	public function createComponentVariantPicker(): VariantPicker
	{
		assert($this->object instanceof ProductLocalization);
		return $this->variantPickerFactory->create($this->object, $this->variant, $this->setup);
	}

	public function createComponentManualsToDownload(): ManualsToDownloadComponent
	{
		assert($this->object instanceof ProductLocalization);
		return $this->manualsToDownloadComponentFactory->create($this->object);
	}

	public function createComponentFaqProductDetail(): FaqProductDetailComponent
	{
		assert($this->object instanceof ProductLocalization);
		return $this->faqProductDetailComponentFactory->create($this->object->product, $this->mutation);
	}

	public function createComponentSpecialistReview(): SpecialistReview
	{
		assert($this->object instanceof ProductLocalization);
		return $this->specialistReviewFactory->create($this->object->product, $this->mutation);
	}

	public function createComponentAlternativeProducts(): AlternativeProducts
	{
		assert($this->object instanceof ProductLocalization);
		return $this->alternativeProductsFactory->create($this->object, $this->setup);
	}

	public function createComponentAddToCompareComponent(): AddToCompareComponent
	{
		assert($this->object instanceof ProductLocalization);
		return $this->addToCompareComponentFactory->create($this->object->product, $this->setup, [
			'from' => 'productDetail'
		]);
	}

}
