<?php declare(strict_types = 1);

namespace App\FrontModule\Components\SignInForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\FacebookLogin\FacebookLogin;
use App\FrontModule\Components\FacebookLogin\FacebookLoginFactory;
use App\FrontModule\Components\GoogleLogin\GoogleLogin;
use App\FrontModule\Components\GoogleLogin\GoogleLoginFactory;
use App\FrontModule\Components\SeznamLogin\SeznamLogin;
use App\FrontModule\Components\SeznamLogin\SeznamLoginFactory;
use App\Model\AdminEmails;
use App\Model\Form\FormThrottler;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\DI\Attributes\Inject;
use Nette\Http\Response;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property-read DefaultTemplate $template
 */
final class SignInForm extends UI\Control
{

	public const COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR = 'loginEmailAuthError';

	#[Inject]
	public GoogleLoginFactory $googleLoginFactory;

	#[Inject]
	public FacebookLoginFactory $facebookLoginFactory;

	#[Inject]
	public SeznamLoginFactory $seznamLoginFactory;

	public function __construct(
		private readonly Mutation $mutation,
		private readonly object $object,
		private readonly TranslatorDB $translator,
		private readonly Response $httpResponse,
		private readonly Orm $orm,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly AdminEmails $adminEmails,
		private readonly FormThrottler $formThrottler,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/signInForm.latte');
	}


	/**
	 * Sign-in form factory.
	 *
	 * @return UI\Form
	 * @throws UI\InvalidLinkException
	 */
	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->addEmail('username', 'form_label_email_address')->setRequired('form_enter_username');

		$params = [];
		if (isset($this->presenter->params['p'])) {
			$form->addHidden('p', $this->presenter->params['p']);
			$params['p'] = $this->presenter->params['p'];
		}

		$form->addPassword('password', 'form_label_password')
			->setRequired('form_enter_password');

		// $form->addCheckbox('remember', 'form_label_sign_remember');

		$form->addSubmit('send', 'Sign in');

		$url = $this->presenter->link($this->mutation->pages->userLogin, $params);
		$form->setAction($url);

		if (isset($_GET['email'])) {
			$form->setDefaults(['username' => $_GET['email']]);
		}

		$form->onValidate[] = function (UI\Form $form, ArrayHash $values) {
			if ( ! $this->adminEmails->isDeveloperEmail($values->username) && ! $values->password) {
				$form['password']->addError('form_enter_password');
			}
		};

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		$this->formThrottler->throttleFormSubmissions($form, formType: 'Front:SignIn', discriminatorName: 'username');

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		bdump($this->getPresenter()->isAjax());
		bdump($form->getErrors());

		if ($this->presenter->isAjax()) {
			$this->getPresenter()->redrawControl('boxLogin');
			$this->redrawControl();
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		if ($this->adminEmails->isDeveloperEmail($values->username)) {
			if ($this->presenter->isAjax()) {
				$this->flashMessage($this->translator->translate('message_login_developer_use_admin'), 'error');
				$this->redrawControl();
				return;
			} else {
				$this->presenter->redirect(':Admin:Sign:default');
			}
		}

		$this->presenter->getUser()->setExpiration('14 days', false);

		try {
			$this->presenter->getUser()->login($values->username, $values->password);

			// last login time
			$userEntity = $this->orm->user->getById($this->presenter->getUser()->id);

			$userEntity->lastLogin = new DateTimeImmutable();
			$this->orm->user->persistAndFlush($userEntity);

			$this->presenter->getHttpResponse()->setCookie('loginComplete', $this->getParent()?->getName(), '+1 minute');

			if ($this->presenter->isAjax()) {
				$this->flashMessage($this->translator->translate('message_ok_login'), 'ok');
				$this->redrawControl();
			} else {
				$this->presenter->redirect('this');
			}
		} catch (AuthenticationException $e) {
			$form['password']->addError('message_bad_login');
			$form['username']->addError('message_bad_login');
			$this->httpResponse->setCookie(self::COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR, $values->username, 0);
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		return $this->googleLoginFactory->create($this->mutation);
	}

	protected function createComponentFacebookLogin(): FacebookLogin
	{
		return $this->facebookLoginFactory->create($this->mutation);
	}

	protected function createComponentSeznamLogin(): SeznamLogin
	{
		return $this->seznamLoginFactory->create($this->mutation);
	}

}
