<?php

declare(strict_types = 1);

namespace App\FrontModule\Components\CatalogProducts;

use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Services\CookieManager;
use App\FrontModule\Components\ProductBox\ProductBox;
use App\FrontModule\Components\ProductBox\ProductBoxFactory;
use App\Model\ConfigService;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Routable;
use App\Model\Orm\Traits\HasGTMViewport;
use App\Model\Setup;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use Closure;
use Nette\Application\UI\Control;
use Nette\Application\UI\Multiplier;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;
use App\PostType\Banner\FrontModule\Components\BannerBox;
use App\PostType\Banner\FrontModule\Components\BannerBoxFactory;
use App\PostType\Banner\Model\Orm\Banner;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Banner\Model\Orm\BannerLocalizationModel;
use App\PostType\Banner\Model\Orm\BannerLocalizationRepository;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Utils\Paginator;

/**
 * @property-read DefaultTemplate $template
 */
final class CatalogProducts extends Control
{

	use HasGTMViewport;

	/** @var ICollection<Product>  */
	private ICollection $products;

	private int $itemPerPage;

	public array $onAfterRender = [];

	private ?array $productsById = null;

	private array $catalogItemsPerPage;

	private array $ratingLocalizationsByCatalogTree = [];

	/**
	 * @phpstan-param Closure(int $limit, int $offset): CatalogProductsData $findCatalogProductsDataCallback
	 */
	public function __construct(
		private readonly Routable|StaticPage $routable,
		private readonly Setup $setup,
		private readonly Closure $findCatalogProductsDataCallback,
		private readonly int $pageNumber,
		private readonly array $paramsToTemplate,
		private readonly string $scriptId,
		private readonly string $listId,
		private readonly TranslatorDB $translator,
		private readonly ProductBoxFactory $productBoxFactory,
		private readonly ConfigService $configService,
		private readonly VisualPaginatorFactory $visualPaginatorFactory,
		private readonly BannerBoxFactory $bannerBoxFactory,
		private readonly BannerLocalizationModel $bannerLocalizationModel,
		private readonly BannerLocalizationRepository $bannerLocalizationRepository,
		private readonly CookieManager $cookieManager,
		private readonly Orm $orm,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	public function setItemPerPage(int $itemPerPage): void
	{
		$this->itemPerPage = $itemPerPage;
	}

	private function init(): void
	{
		$itemsPerPage = $this->itemPerPage ?? $this->configService->get('shop', 'productsPaging');

		$catalogProductsData = ($this->findCatalogProductsDataCallback)(100000, 0);

		assert($catalogProductsData instanceof CatalogProductsData);
		$products = $catalogProductsData->products;

		assert($this->routable instanceof Tree);
		$banners = $this->bannerLocalizationModel->getBannersFromPath($this->routable);

		$this->products = $products;

		$catalogItems = $this->getCatalogItems($products->fetchAll(), $banners->fetchAll(), $itemsPerPage);

		$this->catalogItemsPerPage = $this->getCatalogItemsPerPage($catalogItems, $this->pageNumber, $itemsPerPage);

		$paginator = $this->setPaginator($itemsPerPage, count($catalogItems));

		$this->initGTMViewport($this->products, $this->scriptId, $this->listId, $this->routable->getNameTitle(), $paginator->getFirstItemOnPage());

		$this->ratingLocalizationsByCatalogTree = $this->getRatingLocalizationsByCatalogTree();

		foreach ($this->onAfterRender as $afterRender) {
			$afterRender($this->products);
		}
	}

	public function render(): void
	{
		$this->template->render(__DIR__ . '/catalogProducts.latte', [
			'catalogItemsPerPage' => $this->catalogItemsPerPage,
			'products' => $this->products,
			'templates' => FE_TEMPLATE_DIR,
			'showMoreBtn' => true,
			'translator' => $this->translator,
			...$this->paramsToTemplate,
		]);
	}

	/**
	 * @return Multiplier<ProductBox>
	 */
	public function createComponentProductBox(): Multiplier
	{
		if ( $this->productsById === null) {
			$this->productsById = $this->products->fetchPairs('id');
		}

		return new Multiplier(function ($productId) {
			$product = $this->productsById[$productId];
			assert($product instanceof Product);
			assert($this->routable instanceof CatalogTree);
			$parametersToTemplate = [
				'class' => false,
				'showAddToMyLibrary' => true,
				'listId' => '',
				'listName' => '',
				'dataAttrs' => ['gtm-event' => 'view_item_list', 'gtm-item' => $this->template->productsGtmItem[$product->id] ?? null],
				'ratingLocalization' => $this->getPresenter()->getRequest()->getParameter('filter')
					? null
					: $this->ratingLocalizationsByCatalogTree[$product->id] ?? null,
			];

			assert($this->routable instanceof CatalogTree);

			return $this->productBoxFactory->create($product, null, $this->setup, $parametersToTemplate, null, $this->routable);
		});
	}

	/**
	 * @return Multiplier<BannerBox>
	 */
	public function createComponentBannerBox(): Multiplier
	{
		return new Multiplier(function ($bannerLocalizationId): BannerBox {
			$bannerLocalization = $this->bannerLocalizationRepository->getById($bannerLocalizationId);
			assert($bannerLocalization instanceof BannerLocalization);
			return $this->bannerBoxFactory->create($bannerLocalization, $this->setup->mutation);
		});
	}

	public function createComponentPager(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}

	public function getTotalCount(): int
	{
		return $this->products->count();
	}

	private function setPaginator(int $itemsPerPage, int $totalItemsCount): Paginator
	{
		$this['pager']->object  = $this->routable;
		$this['pager']->special = true;
		$this['pager']->setTranslator($this->translator);

		return $this['pager']
			->getPaginator()
			->setPage($this->pageNumber)
			->setItemsPerPage($itemsPerPage)
			->setItemCount($totalItemsCount);
	}

	private function getCatalogItems(array $products, array $banners, int $itemsPerPage): array
	{
		$catalogItems = [];
		$pageNumber = 1;

		while (!empty($products)) {
			$hash = (int) $this->cookieManager->getOrSet('firstBannerPosition', 3600, fn(): string
				=> (string) crc32(($this->routable->id ?? 1) . rand(1, 100)));

			srand($hash + $pageNumber);
			$firstBannerPosition = rand(5, 7);

			srand($hash + $pageNumber + 1);
			$secondBannerPosition = rand($firstBannerPosition + 4, $itemsPerPage - 1);

			for ($position = 1; $position <= $itemsPerPage; $position++) {
				if ($position === $firstBannerPosition) {
					$this->addBannerToCatalogItems($catalogItems, $banners, $firstBannerPosition);
				}

				if ($position === $secondBannerPosition) {
					$this->addBannerToCatalogItems($catalogItems, $banners, $secondBannerPosition);
				}

				$catalogItems[] = array_shift($products);

				if (count($catalogItems) % $itemsPerPage === 0 || count($products) === 0) {
					unset($firstBannerPosition);
					unset($secondBannerPosition);
					unset($position);
					$pageNumber++;
					break;
				}
			}
		}

		return $catalogItems;
	}

	private function getCatalogItemsPerPage(array $catalogItems, int $page, int $itemsPerPage): array
	{
		return array_slice($catalogItems, ($page - 1) * $itemsPerPage, $itemsPerPage);
	}

	private function addBannerToCatalogItems(array &$catalogItems, array &$banners, int &$bannerPosition): void
	{
		if (!isset($banners[0])) {
			return;
		}

		$banner = $banners[0];

		if ($banner->getParent()->type === Banner::TYPE_SINGLE_SLOT) {
			$catalogItems[] = array_shift($banners);
		}

		if ($banner->getParent()->type === Banner::TYPE_DOUBLE_SLOT) {
			if ((count($catalogItems) + 1) % 3 === 0) {
				$bannerPosition++;
			} else {
				$catalogItems[] = array_shift($banners);
				$catalogItems[] = [];
			}
		}
	}

	private function getRatingLocalizationsByCatalogTree(): array
	{
		$ratingLocalizationsByCatalogTree = [];

		assert($this->routable instanceof CatalogTree);

		$ratingLocalizations = $this->getRatingLocalizations($this->routable);

		foreach ($ratingLocalizations as $ratingLocalization) {
			assert($this->routable instanceof CatalogTree);
			$productId = $ratingLocalization->getProductIdByTree($this->routable);

			if ($productId === null) {
				continue;
			}

			if (isset($ratingLocalizationsByCatalogTree[$productId])) {
				continue;
			}

			$ratingLocalizationsByCatalogTree[$productId] = $ratingLocalization;
		}

		return $ratingLocalizationsByCatalogTree;
	}

	private function getRatingLocalizations(CatalogTree $catalogTree): array
	{
		$recursiveRatingLocalizations = $this->getRecursiveRatingLocalizationsByTree($catalogTree);
		$ratingLocalizationsWithManualProduct = $this->getRatingLocalizationsWithManualProductByCatalogTree($catalogTree);

		return array_merge($ratingLocalizationsWithManualProduct, $recursiveRatingLocalizations);
	}

	public function getRecursiveRatingLocalizationsByTree(CatalogTree $catalogTree): array
	{
		$ratingLocalizations = $this->orm->ratingLocalization->findBy([
				'tree' => $catalogTree,
			])
			->orderBy('sort', 'ASC');

		if ($ratingLocalizations->count() === 0) {
			$parent = $catalogTree->parent;

			if ($parent === null) {
				return [];
			}

			if (!($parent instanceof CatalogTree)) {
				return [];
			}

			return $this->getRecursiveRatingLocalizationsByTree($parent);
		}

		$ratingLocalizationsArray = [];

		foreach ($ratingLocalizations as $ratingLocalization) {
			$ratingLocalizationsArray[$ratingLocalization->name] = $ratingLocalization;
		}

		return $ratingLocalizationsArray;
	}

	private function getRatingLocalizationsWithManualProductByCatalogTree(CatalogTree $catalogTree): array
	{
		$categories = array_merge($catalogTree->path, [$catalogTree->id]);

		$ratingLocalizations = $this->orm->ratingLocalization->findBy([
				'tree->id' => $categories,
			])
			->orderBy('sort', 'ASC');

		$ratingLocalizationsArray = [];

		foreach ($ratingLocalizations as $ratingLocalization) {
			if ($ratingLocalization->getManualProduct() !== null) {
				$ratingLocalizationsArray[$ratingLocalization->name] = $ratingLocalization;
			}
		}

		return $ratingLocalizationsArray;
	}

}
