<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer;

use App\FrontModule\Components\CustomContentRenderer\Components\ArticlesCC\ArticlesCC;
use App\FrontModule\Components\CustomContentRenderer\Components\ArticlesCC\ArticlesCCFactory;
use App\FrontModule\Components\CustomContentRenderer\Components\ProductsCC\ProductsCC;
use App\FrontModule\Components\CustomContentRenderer\Components\ProductsCC\ProductsCCFactory;
use App\FrontModule\Components\CustomContentRenderer\Components\ProductsSimplifiedCC\ProductsSimplifiedCC;
use App\FrontModule\Components\CustomContentRenderer\Components\ProductsSimplifiedCC\ProductsSimplifiedCCFactory;
use App\Infrastructure\Latte\Filters;
use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Setupable;
use App\Model\Orm\State\State;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\IEntity;
use App\Model\CustomContent\CustomContent;
use App\Model\Setup;
use App\Model\SetupFactory;
use stdClass;

/**
 * @property-read DefaultTemplate $template
 */
final class CustomContentRenderer extends UI\Control implements Setupable
{
	protected Setup $setup;

	public array $props = [];

	public function __construct(
		private readonly IEntity $object,
		private readonly Mutation $mutation,
		private readonly PriceLevel $priceLevel,
		private readonly State $state,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
		private readonly CustomContent $customContent,
		private readonly SetupFactory $setupFactory,
		private readonly ProductsSimplifiedCCFactory $productsSimplifiedCCFactory,
		private readonly ProductsCCFactory $productsCCFactory,
		private readonly ArticlesCCFactory $articlesCCFactory,
	)
	{
		$this->setupFactory->init([
			$this
		], $this->mutation, $this->state, $this->priceLevel);
	}

	public function setSetup(Setup $setup): Setupable
	{
		$this->setup = $setup;
		return $this;
	}

	public function render(array $props = []): void
	{
		$this->props = $props;
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->templates = FE_TEMPLATE_DIR;
		$template->defaultTemplateDirectory = FE_TEMPLATE_DIR . '/part/customContent';
		$template->isDev = $this->configService->get('isDev');
		$template->props = $props;
		$template->object = $this->object;
		$template->mutation = $this->mutation;
		$template->pages = $this->mutation->pages;
		$template->priceLevel = $this->priceLevel;
		$template->state = $this->state;
		$template->defaultObjectCC = $this->getCC();
		$isDemo = (isset($this->object->parent->uid) && $this->object->parent->uid == "ccDemo") || (isset($this->object->uid) && $this->object->uid == 'ccDemo');
		$this->template->isDemo = $isDemo;
		if ($isDemo) {
			assert(method_exists($this->object, 'getCcModules'));
			$this->template->allCustomComponentsLabels = $this->customContent->getAllCustomComponentsLabels((array) $this->object->getCcModules());
		} else {
			$this->template->allCustomComponentsLabels = [];
		}

		$template->addFilter('parseVideoId', Filters::parseVideoId(...));
		$template->render(__DIR__ . '/customContentRenderer.latte');
	}

	private function getCC(): array
	{
		assert(method_exists($this->object, 'getCcModules'));
		if ($this->object->getCcModules()) {
			assert(isset($this->object->cc));
			return (array) ($this->object->cc ?? []);
		}

		return [];
	}

	public function getCCByName(string $name): stdClass
	{
		foreach ($this->getCC() as $key => $cc) {
			$length = strpos($key, '____');
			if ($length === false) {
				continue;
			}
			$contentName = substr($key, 0, $length);
			if ($contentName === $name) {
				return (object) $cc[0];
			}
		}

		return (object) [];
	}

	public function createComponentProductsSimplifiedCC(): ProductsSimplifiedCC
	{
		return $this->productsSimplifiedCCFactory->create($this->getCCByName('products_simplified'), $this->setup);
	}

	public function createComponentProductsCC(): ProductsCC
	{
		return $this->productsCCFactory->create($this->getCCByName('products'), $this->setup, $this->getCCByName('products_simplified'));
	}

	public function createComponentArticlesCC(): ArticlesCC
	{
		return $this->articlesCCFactory->create($this->getCCByName('articles'), $this->mutation);
	}

}
