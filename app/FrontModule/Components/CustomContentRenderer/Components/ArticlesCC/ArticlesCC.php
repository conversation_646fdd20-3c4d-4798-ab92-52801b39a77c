<?php 

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer\Components\ArticlesCC;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;

/**
 * @property-read DefaultTemplate $template
 */
class ArticlesCC extends Control
{
    public function __construct(
        private readonly \stdClass $ccItem,
		private readonly Mutation $mutation,
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
    )
    {
    }

    public function render(array $props = []): void
    {
        $this->template->render(__DIR__ . '/articlesCC.latte', [
			'templates' => FE_TEMPLATE_DIR,
			'articles' => $this->getArticlesByTags($this->ccItem->tags ?? null),
            'customContentItem' => $this->ccItem,
            'mutation' => $this->mutation,
			'props' => $props,
        ]);
    }

	/**
     * @param ICollection<BlogTag> $tags
     * @return ICollection<BlogLocalization>
     */
    public function getArticlesByTags(?ICollection $tags): ICollection
    {       
        if ($tags === null) {
            /** @var ICollection<BlogLocalization> $collection */
            $collection = new EmptyCollection;
            return $collection;
        }

        if (isset($this->ccItem->carousel) && $this->ccItem->carousel === true) {
            $limit = 10;
        } else {
            $limit = 3;
        }

        return $this->blogLocalizationRepository->findBy([
                'blog->tags->id' => $tags?->fetchPairs(null, 'id'),
                'mutation' => $this->mutation,
            ])
            ->orderBy('publicFrom', 'DESC')
            ->limitBy($limit);
    }
}