<?php 

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer\Components\ClientTestimonialCC;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\ClientTestimonial\Model\Orm\Localization\ClientTestimonialLocalizationRepository;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;
use App\PostType\ClientTestimonial\Model\Orm\Localization\ClientTestimonialLocalization;

/**
 * @property-read DefaultTemplate $template
 */
class ClientTestimonialCC extends Control
{
    public function __construct(
        private readonly \stdClass $ccItem,
		private readonly Mutation $mutation,
		private readonly ClientTestimonialLocalizationRepository $clientTestimonialLocalizationRepository,
    )
    {
    }

    public function render(array $props = []): void
    {
        $clientTestimonials = $this->getClientTestimonials($this->ccItem->clientTestimonialItems ?? null);

        if ($clientTestimonials === []) {
            return;
        }

        $this->template->render(__DIR__ . '/clientTestimonialCC.latte', [
			'templates' => FE_TEMPLATE_DIR,
			'clientTestimonials' => $clientTestimonials,
            'customContentItem' => $this->ccItem,
            'mutation' => $this->mutation,
			'props' => $props,
        ]);
    }

    /**
     * @param ICollection<ClientTestimonialLocalization> $clientTestimonialItems
     */
    public function getClientTestimonials(?ICollection $clientTestimonialItems): array
    {
        $clientTestimonials = [];

        if ($clientTestimonialItems === null) {
            return [];
        }

        $clientTestimonialLocalizations = $this->clientTestimonialLocalizationRepository->findBy([
                'clientTestimonial->id' => $clientTestimonialItems->fetchPairs(null, 'id'),
                'mutation' => $this->mutation,
            ])
            ->orderBy('sort', 'ASC');
            
        if ($clientTestimonialLocalizations->count() < 3) {
            return [];
        }

        foreach ($clientTestimonialLocalizations as $clientTestimonialLocalization) {
            $data = [
                'name' => $clientTestimonialLocalization->name,
                'imgPerson' => $clientTestimonialLocalization->cf->settings->imgPerson ?? null,
                'imgCompany' => $clientTestimonialLocalization->cf->settings->imgCompany ?? null,
                'text' => $clientTestimonialLocalization->cf->settings->text ?? null,
                'info' => $clientTestimonialLocalization->cf->settings->info ?? null,
            ];
            $clientTestimonials[] = (object) $data;
        }

        if (count($clientTestimonials) < 3) {
            return [];
        }

        return $clientTestimonials;
    }
}