{default $class = false}

{if ($showParameters)}
	{cache 'productDetailParameters-P-' . $product->id, expire: $product->getTemplateCacheExpire(), tags: $product->getTemplateCacheTags()}
		<div class="u-mb-last-0">
			{* Header parameters *}
			<ul n:if="count($headerProductParameters) > 0" class="tw-mb-[0.8rem] u-reset-ul tw-inline-flex tw-flex-wrap tw-gap-[3.2rem] tw-text-[1.2rem] tw-overflow-hidden xxl:tw-hidden">
				<li n:foreach="$headerProductParameters as $name => $value" class="{if !$iterator->isLast()}tw-relative before:tw-content-[''] before:tw-absolute before:tw-right-[-1.6rem] before:tw-top-0 before:tw-bottom-0 before:tw-w-[0.1rem] before:tw-bg-bd{/if}">
					{if $value instanceof App\Model\Orm\ParameterValue\ParameterValue}
						{$value->parameter?->title}: {include #paramValue, parameterValue: $value, parameter: $value->parameter, aClass: 'tw-text-inherit tw-decoration-inherit'}
					{else}
						{$name}: {$value}
					{/if}
				</li>
			</ul>

			{* TODO: Highlighted parameters *}
			<div n:if="count($mainProductParameterValues) > 0" class="tw-@container">
				<ul class="u-reset-ul tw-grid xs:tw-grid-cols-2 xs:@[100rem]:tw-grid-cols-4 tw-gap-[1.6rem] md:tw-gap-[4rem] tw-mb-[2.8rem] tw-border-bd tw-border-solid tw-border-[0.1rem] tw-rounded-md md:tw-rounded-xl tw-p-[1.6rem] md:tw-p-[4rem]">
					<li n:foreach="$mainProductParameterValues as $parameterValue">
						{var $parameter = $parameterValue->parameter}
						<b class="tw-text-[1.4rem] md:tw-text-[1.5rem]">{$parameter->title}:</b>
						<b class="tw-block tw-text-[2rem] md:tw-text-[3rem]">{$parameter->getIcon()} {$parameterValue->value}</b>
						<span class="tw-text-help tw-text-[1.3rem] md:tw-text-[1.4rem]">
							{$parameter->getDescription($mutation)}
							{* Více *}

							{php $link = $parameter->getLink()}
							{if $link}
								{php $type = $link->toggle ?? 'customHref'}
								{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() ?? false : false}
								{php $hrefName = $type == 'systemHref' ? ($link->systemHref??->hrefName ?? false) : ($link->customHref??->hrefName ?? false)}
								{php $customHref = $link->customHref??->href ?? false}
								{php $href = $type == 'systemHref' && $page ? $presenter->link($page) : ($type == 'customHref' && $customHref && $hrefName ? $customHref : false)}
								{php $text = $hrefName ?: ($page ? $page->nameAnchor : false)}
								{php $blank = $type == 'customHref' && $customHref && !(strpos($customHref, '#') === 0)}

								<a href="{$href}" n:ifcontent class="tw-text-inherit tw-decoration-inherit">
									{$text}
								</a>
							{/if}
						</span>

					</li>
				</ul>
			</div>

			{* Table parameters *}
			{php $isLimited = count($tableProductParameterValues) > 10}
			<div n:if="count($tableProductParameterValues) > 0" n:class="'tw-@container', $isLimited ? tw-group"{if $isLimited} data-controller="toggle-class"{/if}>
				<div n:tag-if="$isLimited" class="tw-max-h-[21.4rem] tw-overflow-hidden tw-relative before:tw-content-[''] before:tw-absolute before:tw-left-0 before:tw-right-0 before:tw-bottom-0 before:tw-h-[1.2rem] before:tw-bg-gradient-to-t before:tw-from-[rgba(0,0,0,0.1)] before:tw-to-transparent group-[.is-open]:tw-max-h-none group-[.is-open]:before:tw-content-none">
					<ul class="u-reset-ul @[100rem]:tw-columns-2 tw-gap-[1.2rem] tw-text-[1.5rem] tw-mb-[-0.4rem]">
						<li n:foreach="$tableProductParameterValues as $parameterValue" class="tw-break-inside-avoid !tw-bg-bg tw-rounded-md tw-flex !tw-mb-[0.4rem] tw-items-center tw-justify-between !tw-p-[1rem_1.8rem] tw-gap-[1.6rem]">
							{if $parameterValue instanceof ArrayIterator}
								{foreach $parameterValue as $valueObject}
									{var $parameter = $valueObject->parameter}
								{/foreach}
							{else}
								{var $parameter = $parameterValue->parameter}
							{/if}

							{$parameter->title}:
							<b>
								{if $parameter->type == "multiselect"}
									{foreach $parameterValue as $valueObject}
										{include #paramValue, parameterValue: $valueObject, parameter: $parameter}
									{/foreach}
								{else}
									{include #paramValue, parameterValue: $parameterValue, parameter: $parameter}
								{/if}
							</b>
						</li>
					</ul>
				</div>
				<p n:if="$isLimited" class="tw-text-center tw-mb-0 tw-mt-[2.4rem] group-[.is-open]:tw-hidden">
					<button type="button" class="as-link btn btn--bd" data-action="toggle-class#toggle">
						<span class="btn__text">
							<span class="btn__inner">
								{_"btn_show_all_parameters"}
								{('angle-down')|icon, 'btn__icon'}
							</span>
						</span>
					</button>
				</p>
			</div>
		</div>
		{define #paramValue}
			{default $aClass = false}

			{if in_array($parameter->uid, $filterableUids)}
				{var $filter = ['dials' => [$parameter->uid => [$parameterValue->id => $parameterValue->id]]]}
				{capture $link}{plink $mainCategory, 'filter' => $filter}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}

				<a href="{$link}" n:class="$aClass">
					{$parameterValue->value} {if $parameter->type == 'number'}{$parameter->unit}{/if}
				</a>
			{else}
				{$parameterValue->value} {if $parameter->type == 'number'}{$parameter->unit}{/if}
			{/if}
		{/define}
	{/cache}
{/if}

