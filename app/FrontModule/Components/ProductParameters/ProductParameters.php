<?php

declare(strict_types = 1);

namespace App\FrontModule\Components\ProductParameters;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\CustomField\LazyValue;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\TranslatorDB;
use ArrayIterator;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
class ProductParameters extends UI\Control
{
	private array $productParameters = [];
	private array $parameterValues = [];

	public function __construct(
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly Product $product,
		private readonly ProductVariant $variant,
		private readonly CatalogParameter $catalogParameter,
		private readonly Mutation $mutation,
	)
	{
		$this->productParameters = $this->getProductParameters($this->product, $this->variant);
		$this->parameterValues = $this->getParameterValues($this->product, $this->variant);
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		if ($mainCategory = $this->product->mainCategory) {
			$mainPageParameters = $this->catalogParameter->getParametersCfForFilter($mainCategory);
		}

		$filterableUids = [];
		if (isset($mainPageParameters->visibleParameters)) {
			foreach ($mainPageParameters->visibleParameters as $visibleParameter) {
				if (isset($visibleParameter->parameter) && $visibleParameter->parameter instanceof LazyValue && $visibleParameter->parameter->getEntity() !== null) {
					$parameter = $visibleParameter->parameter->getEntity();

					assert($parameter instanceof Parameter);
					
					if ($parameter->type == Parameter::TYPE_NUMBER) {
						if ( ! isset($visibleParameter->numberAsRange) || ! $visibleParameter->numberAsRange) {
							$filterableUids[] = $parameter->uid;
						}
					} else {
						$filterableUids[] = $parameter->uid;
					}
				}
			}
		}

		$this->template
			->setTranslator($this->translator)
			->render(__DIR__ . '/productParameters.latte', [
				'product' => $this->product,
				'mainCategory' => $this->product->mainCategory,
				'filterableUids' => $filterableUids,
				'showParameters' => $this->showParameters(),
				'headerProductParameters' => $this->getHeaderProductParameters(),
				'mainProductParameterValues' => $this->getMainProductParameterValues(),
				'tableProductParameterValues' => $this->getTableProductParameterValues(),
				'mutation' => $this->mutation,
			]);
	}

	private function showParameters(): bool
	{
		if (!isset($this->product->mainCategory->cf)) {
			return true;
		}

		$categoryCf = $this->product->mainCategory->cf;

		if (!isset($categoryCf->categoryParametersForProductDetail->visible)) {
			return true;
		}

		return !$categoryCf->categoryParametersForProductDetail->visible;
	}

	private function getHeaderProductParameters(): array
	{
		$headerProductParameters = [];

		foreach($this->productParameters as $parameter) {
			$headerProductParameters[$parameter->uid] = match($parameter->uid) {
				'znacka' => $this->takeParameterValue('znacka'),
				default => null,
			};
		}

		$fields = [
			'code' => 'product_kod',
			'ean'  => 'ean',
			'isbn' => 'isbn',
			'pN'   => 'p_n',
		];
		
		foreach ($fields as $property => $translationKey) {
			if (!isset($this->variant->{$property})) {
				continue;
			}
		
			$value = $this->variant->{$property};
		
			if (trim((string) $value) !== '' && $value !== 0 && $value !== '0') {
				$headerProductParameters[$this->translator->translate($translationKey)] = $value;
			}
		}

		return array_filter($headerProductParameters, fn($value) => $value !== null && $value !== '');
	}

	private function getMainProductParameterValues(): array
	{
		$mainProductParameterValues = [];

		foreach ($this->parameterValues as $parameterValue) {
			$parameter = $this->getParameterFromValue($parameterValue);

			if ($parameter === null) {
				continue;
			}

			if ($parameter->isMainForDetailPage()) {
				$mainProductParameterValues[$parameter->uid] = $parameterValue;
			}
		}

		return $mainProductParameterValues;
	}

	private function getTableProductParameterValues(): array
	{
		$tableProductParameterValues = [];

		foreach ($this->parameterValues as $parameterValue) {
			$parameter = $this->getParameterFromValue($parameterValue);

			if ($parameter === null) {
				continue;
			}

			if (!$parameter->isMainForDetailPage()) {
				$tableProductParameterValues[$parameter->uid] = $parameterValue;
			}
		}

		return $tableProductParameterValues;
	}

	private function getProductParameters(Product $product, ProductVariant $variant): array
	{
		$parameters = [];

		$productParameters = $this->orm->parameter->findDetailParametersForProduct($product)->fetchAll();

		$variantParameterValues = $variant->getFilledVariantParameterValues();

		$variantParameters = array_map(fn(ParameterValue $parameterValue) => $parameterValue->parameter, $variantParameterValues);

		$mergedParameters = array_filter(array_merge($productParameters, $variantParameters));

		foreach ($mergedParameters as $parameter) {
			assert($parameter instanceof Parameter);
			if ($parameter->isInDetail === false) {
				continue;
			}
			$parameters[$parameter->uid] = $parameter;
		}

		return $parameters;
	}

	private function getParameterValues(Product $product, ProductVariant $variant): array
	{
		$parameterValues = [];

		$variantParameterValues = $this->getVariantParameterValues($variant);
		$productParameterValues = $this->getProductParameterValues($product);

		$mergedParameterValues = array_merge($variantParameterValues, $productParameterValues);

		foreach ($mergedParameterValues as $parameterValue) {
			$parameter = $this->getParameterFromValue($parameterValue);

			if ($parameter === null) {
				continue;
			}

			$parameterValues[$parameter->uid] = $parameterValue;
		}

		return $parameterValues;
	}

	private function takeParameterValue(string $parameterUid): ?ParameterValue
	{
		$parameterValue = $this->parameterValues[$parameterUid] ?? null;

		if ($parameterValue === null) {
			return null;
		}

		unset($this->parameterValues[$parameterUid]);

		return $parameterValue;
	}

	private function getProductParameterValues(Product $product): array
	{
		$productParameterValues = array_map(fn(Parameter $parameter) => 
			$product->getParameterValueByUid($parameter->uid) ?? null
		, $this->productParameters);

		foreach ($productParameterValues as $key => $parameterValue) {
			if ($parameterValue === null) {
				unset($productParameterValues[$key]);
				continue;
			}

			if ($parameterValue instanceof ArrayIterator) {
				if ($parameterValue->count() === 0) {
					unset($productParameterValues[$key]);
				}
				continue;
			}

			if ($parameterValue->value === '' || $parameterValue->value === null) {
				unset($productParameterValues[$key]);
				continue;
			}
		}

		return $productParameterValues;
	}

	private function getVariantParameterValues(ProductVariant $variant): array
	{
		$variantParameterValues = $variant->getFilledVariantParameterValues();

		foreach ($variantParameterValues as $key => $parameterValue) {
			if ($parameterValue === null) {
				unset($variantParameterValues[$key]);
				continue;
			}

			if ($parameterValue instanceof ArrayIterator) {
				if ($parameterValue->count() === 0) {
					unset($variantParameterValues[$key]);
				}
				continue;
			}

			if ($parameterValue->value === '' || $parameterValue->value === null) {
				unset($variantParameterValues[$key]);
				continue;
			}
		}

		return $variantParameterValues;
	}

	/**
	 * @param ParameterValue|ArrayIterator<int, ParameterValue> $parameterValue
	 */
	private function getParameterFromValue(ParameterValue|ArrayIterator $parameterValue): ?Parameter
	{
		if ($parameterValue instanceof ArrayIterator) {
			foreach ($parameterValue as $value) {
				if ($value !== null) {
					return $value->parameter;
				}
			}
		}
		else {
			return $parameterValue->parameter;
		}

		return null;
	}
}
