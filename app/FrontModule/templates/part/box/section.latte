{default $class = false}
{default $title = false}
{default $titleShort = false}
{default $id = false}
{default $showProductName = false}
{default $condition = true}
{default $flag = true}
{default $hideTitleOnDesktop = false}
{default $forceOpen = false}
{default $extra = false}

<div id="{$id}" n:if="$condition" n:class="b-sections__section, $class, $forceOpen ? is-open" data-sections-target="section" data-controller="toggle-class">
	<div n:class="b-sections__title-wrap, $hideTitleOnDesktop ? 'u-d-n@lg', $forceOpen ? is-open" data-action="click->toggle-class#toggle">
		<h2 class="b-sections__title tw-mb-0">
			<span n:tag-if="$titleShort" class="u-d-n u-d-ib@lg">
				{_$title}
			</span>
			<span n:if="$titleShort" class="u-d-n@lg">
				{_$titleShort}
			</span>
			<span n:if="$showProductName" class="b-sections__name">{$object->name}</span>
		</h2>

		{$extra|noescape}

		<span n:if="$flag" n:class="b-sections__flag, !($flag['showInTitleOnDesktop'] ?? false) ? 'u-d-n@lg', flag, $flag['class'] ?? false">
			{php $icon = $flag['icon'] ?? false}
			{php $iconPosition = $flag['iconPosition'] ?? 'before'}

			{if $icon && $iconPosition == 'before'}{($flag['icon'])|icon, 'flag__icon'}{/if}
			{$flag['text']}
			{if $icon && $iconPosition == 'after'}{($flag['icon'])|icon, 'flag__icon'}{/if}
		</span>

		{('angle-down')|icon, 'b-sections__arrow'}
	</div>
	<div class="b-sections__box u-mb-last-0">
		{block content}{/block}
	</div>
</div>
