

{var $contentProps = [
	listFormCointainer: $props['listFormCointainer'],
	listPlaceholder: $props['listPlaceholder'],
	listSearchUrl: $props['listSearchUrl'],
	listName: $props['listName'],
	dragdrop: (isset($props['dragdrop'])) ? $props['dragdrop'] : false,
	showAdd: (isset($props['showAdd'])) ? $props['showAdd'] : true,
	singleValue: (isset($props['singleValue'])) ? (bool) $props['singleValue'] : false,
	disabled: (isset($props['disabled'])) ? (bool) $props['disabled'] : false,
]}

{var $props = [
	title: $props['title'],
	id: $props['id'],
	icon: $props['icon'],
	open: (isset($props['open'])) ? $props['open'] : false,
	variant: (isset($props['variant'])) ? $props['variant'] : 'main',
	classes: (isset($props['classes'])) ? $props['classes'] : ['u-mb-xxs'],

]}


{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		{var $items = []}

		{if $contentProps['singleValue']}
			{var $containerItems = [$contentProps['listFormCointainer']]}
		{else}
			{var $containerItems = $contentProps['listFormCointainer']->components}
		{/if}

		{foreach $containerItems as $key=>$relationContainer}

			{continueIf $key === 'newItemMarker'}

			{var $item = [
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($contentProps['listSearchUrl'])
				],
				inps: [
					[
						placeholder: $contentProps['listPlaceholder'],
						input: $relationContainer['name'],
						data: [
							suggestinp-target: 'input',
						],
						disabled: $contentProps['disabled'],
					],
					[
						input: $relationContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden',
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/times.svg',
						tooltip: 'Vymazat',
						variant: 'clear',
						data: [
							action: 'SuggestInp#clear'
						]
					],

				]
			]}

			{if $contentProps['showAdd']}
				{php $item['btnsAfter'][] = [
					icon: $templates.'/part/icons/trash.svg',
					tooltip: 'Odstranit',
					variant: 'remove',
					data: [
						action: 'RemoveItem#remove'
					]
				]}
			{/if}

			{php $items[] = $item}
		{/foreach}


		<div class="">



			{var $listProps = [
				data: [
					controller: 'List',
					List-name-value:  $contentProps['listName'],
				],
				listData: [
					List-target: 'list',
				],
				addData: [
					action: 'List#add',
				],
				add: $contentProps['showAdd'],
				dragdrop: $contentProps['dragdrop'],
				items: $items,
			]}



			{include $templates.'/part/box/list.latte',
				props: $listProps
			}
		</div>


	{/block}
{/embed}
