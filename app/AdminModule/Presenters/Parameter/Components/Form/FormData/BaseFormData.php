<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Parameter\Components\Form\FormData;

use App\PostType\Core\AdminModule\Components\Form\FormData\FormDataHelper;
use stdClass;

final class BaseFormData
{

	public ?string $cf;

	public function __construct(
		public readonly string $name,
		public readonly array $mutations,
		public readonly ?string $uid = null,
		public readonly ?string $extId = null,
		public readonly ?string $typeSort = null,
		public readonly ?bool $isProtected = false,
		public readonly ?bool $isInFilter = false,
		public readonly ?bool $isInDetail = false,
		public readonly ?bool $variantParameter = false,
		public readonly ?string $type = null,
		public readonly ?string $productType = null,
		public readonly ?int $parameterGroupId = null,
		public readonly ?string $bestRule = null,
		?stdClass $customFieldContainer = null,
	)
	{
		$this->cf = FormDataHelper::convertEmptyValueToValidJsonObject($customFieldContainer?->cf ?? null);
	}

}
