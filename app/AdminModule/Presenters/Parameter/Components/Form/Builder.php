<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Parameter\Components\Form;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ProductType\ProductType;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use App\Model\Orm\User\User;
use App\Model\Orm\Parameter\Parameter;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;
use App\PostType\ParameterGroup\Model\Orm\ParameterGroupRepository;

final readonly class Builder
{

	public function __construct(
		private readonly CoreBuilder $coreBuilder,
		private readonly MutationsHolder $mutationsHolder,
		private readonly TranslatorDB $translatorDB,
		private readonly ParameterGroupRepository $parameterGroupRepository,
	)
	{
	}

	public function build(\Nette\Application\UI\Form $form, Parameter $parameter, User $user): void
	{
		$this->addCommonToForm($form, $parameter, $user);
		$this->addTranslatiosn($form, $parameter);
		$this->coreBuilder->addButtons($form);
	}

	private function addCommonToForm(UI\Form $form, Parameter $parameter, User $user): void
	{
		$form->addText('name', 'label_internalName');
		$form->addText('uid', 'uid')->setNullable();
		if ($parameter->uid !== null) {
			$form['uid']->setDisabled();
		}
		$form->addText('extId', 'extId')->setNullable();
		$form->addCheckbox('isProtected', 'isProtected');

		$form->addSelect('type', 'type', Parameter::TYPES);
		$form->addSelect('productType', 'productType', ProductType::TYPES)
			->setPrompt('platí pro všechny typy');
		if ($parameter->type !== Parameter::TYPE_TEXT) {
			$form['type']->setDisabled();
		}
		$parameterGroups = $this->parameterGroupRepository->findAll()->fetchPairs('id', 'internalName');

		$form->addSelect('parameterGroupId', 'parameters_label_groupId', $parameterGroups)
			->setPrompt('platí pro všechny skupiny')
			->setDefaultValue(in_array($parameter->parameterGroup?->id, array_keys($parameterGroups))
				? $parameter->parameterGroup->id
				: null);

		$form->addSelect('bestRule', 'parameters_label_bestRule', match ($parameter->type) {
			Parameter::TYPE_NUMBER => [
				Parameter::BIGGER_BEST => 'parameters_bestRule_bigger',
				Parameter::SMALLER_BEST => 'parameters_bestRule_smaller',
			],
			Parameter::TYPE_BOOL => [
				Parameter::TRUE_BEST => 'parameters_bestRule_true',
				Parameter::FALSE_BEST => 'parameters_bestRule_false',
			],
			default => [],
		})
			->setDisabled($parameter->type !== Parameter::TYPE_NUMBER && $parameter->type !== Parameter::TYPE_BOOL)
			->setPrompt('-- default --');

		if (in_array($parameter->type, [
			Parameter::TYPE_MULTISELECT,
			Parameter::TYPE_SELECT,
			Parameter::TYPE_NUMBER,
		])) {
			$form->addSelect('typeSort', 'typeSort', Parameter::SORTS)->setPrompt('-- default --');
			if ($parameter->pendingSort) {
				$form['typeSort']->setDisabled();
			}

			$form->addCheckbox('isInFilter', 'isInFilter');
		}

		if (in_array($parameter->type, [
			Parameter::TYPE_MULTISELECT,
			Parameter::TYPE_SELECT,
			Parameter::TYPE_NUMBER,
			Parameter::TYPE_TEXT,
		])) {
			$form->addCheckbox('isInDetail', 'isInDetail');
		}

		if (in_array($parameter->type, [
			Parameter::TYPE_MULTISELECT,
			Parameter::TYPE_SELECT,
		])) {
			$form->addCheckbox('variantParameter', 'variantParameter');
		}

		if (in_array($parameter->type, [
			Parameter::TYPE_SELECT,
			Parameter::TYPE_NUMBER,
			Parameter::TYPE_TEXT,
			Parameter::TYPE_WYSIWYG,
			Parameter::TYPE_TEXTAREA,
		])) {
			$customFieldContainer = $form->addContainer('customFieldContainer');
			$customFieldContainer->addHidden('cf');
		}

		if ($user->role !== User::ROLE_DEVELOPER) {
			$form['uid']->setDisabled();
			$form['extId']->setDisabled();
			$form['isProtected']->setDisabled();
		}

		$form->setDefaults($parameter->getFormData($form));
	}

	private function addTranslatiosn(UI\Form $form, Parameter $parameter): void
	{
		$mutations = $this->mutationsHolder->findAll(false);
		$mutationsContainer = $form->addContainer('mutations');
		foreach ($mutations as $mutation) {
			$this->translatorDB->setMutation($mutation);
			$mutationContainer = $mutationsContainer->addContainer($mutation->id);
			$mutationContainer->addText('name', 'name',)->setDefaultValue($parameter->title);
			$mutationContainer->addText('tooltip', 'parameters_label_tooltip',)->setDefaultValue($parameter->description);
			$mutationContainer->addText('filterPrefix', 'parameters_label_filter_prefix',)->setDefaultValue($parameter->filterPrefix);
			$mutationContainer->addText('filterPostfix', 'parameters_label_filter_postfix',)->setDefaultValue($parameter->filterPostfix);
			if ($parameter->type === Parameter::TYPE_NUMBER) {
				$mutationContainer->addText('unit', 'parameters_label_unit',)->setDefaultValue($parameter->unit);
			}
		}
	}

}
